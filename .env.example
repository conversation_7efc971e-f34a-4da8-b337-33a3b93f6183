# RHCrypto Bot Environment Configuration

# Robinhood API Credentials
ROBINHOOD_API_KEY=your_api_key_here
ROBINHOOD_PRIVATE_KEY=your_base64_private_key_here

# Server Configuration
PORT=8182
HOST=************

# Network Interface Configuration
# HOST: IP address for web server to listen on
# - Use 0.0.0.0 to listen on all interfaces
# - Use 127.0.0.1 for localhost only
# - Use your Tailscale IP (e.g., ************) for VPN access only
# - Use your LAN IP (e.g., *************) for local network access

# Bot API calls will always use the default network interface
# regardless of the HOST setting above

# Example configurations:
# HOST=0.0.0.0          # Listen on all interfaces (public access)
# HOST=127.0.0.1        # Localhost only (secure, local access)
# HOST=*************    # LAN access (local network)
# HOST=************     # Tailscale VPN access (recommended for security)

# Multi-Currency Configuration
ACTIVE_SYMBOLS=BTC-USD,ETH-USD,DOGE-USD,LTC-USD,BCH-USD,XRP-USD,SOL-USD
INITIAL_DISPLAY_SYMBOL=BTC-USD

# Logging
LOG_LEVEL=info
