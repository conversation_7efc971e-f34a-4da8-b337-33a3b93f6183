// config/SymbolConfig.js
import { RobinhoodAPI } from '../robinhoodClient.js';

/**
 * Symbol Configuration Manager
 * Handles available trading pairs, symbol validation, and symbol metadata
 */
export class SymbolConfig {
  constructor() {
    this.availableSymbols = new Map();
    this.symbolMetadata = new Map();
    this.initialized = false;
    
    // Default supported symbols (will be validated against Robinhood API)
    this.defaultSymbols = [
      {
        symbol: 'BTC-USD',
        assetCode: 'BTC',
        displayName: 'Bitcoin',
        description: 'Bitcoin to USD',
        minOrderSize: 0.00000001,
        maxOrderSize: 1000,
        priceDecimals: 2,
        quantityDecimals: 8
      },
      {
        symbol: 'ETH-USD',
        assetCode: 'ETH',
        displayName: 'Ethereum',
        description: 'Ethereum to USD',
        minOrderSize: 0.000001,
        maxOrderSize: 10000,
        priceDecimals: 2,
        quantityDecimals: 6
      },
      {
        symbol: 'DOGE-USD',
        assetCode: 'DOGE',
        displayName: 'Dogecoin',
        description: 'Dogecoin to USD',
        minOrderSize: 0.01,
        maxOrderSize: 1000000,
        priceDecimals: 4,
        quantityDecimals: 2
      },
      {
        symbol: 'LTC-USD',
        assetCode: 'LTC',
        displayName: 'Litecoin',
        description: 'Litecoin to USD',
        minOrderSize: 0.000001,
        maxOrderSize: 10000,
        priceDecimals: 2,
        quantityDecimals: 6
      },
      {
        symbol: 'BCH-USD',
        assetCode: 'BCH',
        displayName: 'Bitcoin Cash',
        description: 'Bitcoin Cash to USD',
        minOrderSize: 0.0001,
        maxOrderSize: 10000,
        priceDecimals: 2,
        quantityDecimals: 4
      },
      {
        symbol: 'XRP-USD',
        assetCode: 'XRP',
        displayName: 'XRP',
        description: 'XRP to USD',
        minOrderSize: 0.01,
        maxOrderSize: 1000000,
        priceDecimals: 4,
        quantityDecimals: 2
      },
      {
        symbol: 'SOL-USD',
        assetCode: 'SOL',
        displayName: 'Solana',
        description: 'Solana to USD',
        minOrderSize: 0.001,
        maxOrderSize: 10000,
        priceDecimals: 2,
        quantityDecimals: 3
      }
    ];
  }

  /**
   * Initialize symbol configuration by fetching available trading pairs from Robinhood
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    console.log('🔧 Initializing Symbol Configuration...');
    
    try {
      // Fetch available trading pairs from Robinhood
      const tradingPairs = await RobinhoodAPI.getTradingPairs();
      
      if (tradingPairs && tradingPairs.results) {
        this.processRobinhoodTradingPairs(tradingPairs.results);
      } else {
        console.warn('⚠️  No trading pairs received from Robinhood, using defaults');
        this.useDefaultSymbols();
      }
      
      this.initialized = true;
      console.log(`✅ Symbol Configuration initialized with ${this.availableSymbols.size} symbols`);
      
    } catch (error) {
      console.error('❌ Failed to initialize symbol configuration:', error.message);
      console.log('📋 Using default symbol configuration');
      this.useDefaultSymbols();
      this.initialized = true;
    }
  }

  /**
   * Process trading pairs from Robinhood API response
   */
  processRobinhoodTradingPairs(tradingPairs) {
    for (const pair of tradingPairs) {
      if (pair.symbol && pair.asset_currency && pair.quote_currency === 'USD') {
        const defaultMeta = this.defaultSymbols.find(s => s.symbol === pair.symbol);
        
        const metadata = {
          symbol: pair.symbol,
          assetCode: pair.asset_currency.code,
          displayName: defaultMeta?.displayName || pair.asset_currency.name || pair.asset_currency.code,
          description: `${pair.asset_currency.name || pair.asset_currency.code} to USD`,
          minOrderSize: parseFloat(pair.min_order_size || defaultMeta?.minOrderSize || 0.00000001),
          maxOrderSize: parseFloat(pair.max_order_size || defaultMeta?.maxOrderSize || 1000000),
          priceDecimals: defaultMeta?.priceDecimals || 2,
          quantityDecimals: defaultMeta?.quantityDecimals || 8,
          tradingState: pair.state || 'active'
        };
        
        // Only add active trading pairs
        if (metadata.tradingState === 'active') {
          this.availableSymbols.set(pair.symbol, true);
          this.symbolMetadata.set(pair.symbol, metadata);
        }
      }
    }
  }

  /**
   * Use default symbols when API is unavailable
   */
  useDefaultSymbols() {
    for (const symbolData of this.defaultSymbols) {
      this.availableSymbols.set(symbolData.symbol, true);
      this.symbolMetadata.set(symbolData.symbol, symbolData);
    }
  }

  /**
   * Check if a symbol is valid and available for trading
   */
  isValidSymbol(symbol) {
    return this.availableSymbols.has(symbol);
  }

  /**
   * Get metadata for a specific symbol
   */
  getSymbolMetadata(symbol) {
    return this.symbolMetadata.get(symbol);
  }

  /**
   * Get all available symbols
   */
  getAvailableSymbols() {
    return Array.from(this.availableSymbols.keys());
  }

  /**
   * Get symbols formatted for UI display
   */
  getSymbolsForDisplay() {
    return Array.from(this.symbolMetadata.values()).map(meta => ({
      value: meta.symbol,
      label: `${meta.displayName} (${meta.symbol})`,
      assetCode: meta.assetCode,
      displayName: meta.displayName
    }));
  }

  /**
   * Get asset code from symbol (e.g., 'BTC-USD' -> 'BTC')
   */
  getAssetCode(symbol) {
    const metadata = this.symbolMetadata.get(symbol);
    return metadata?.assetCode || symbol.split('-')[0];
  }

  /**
   * Get display name for symbol
   */
  getDisplayName(symbol) {
    const metadata = this.symbolMetadata.get(symbol);
    return metadata?.displayName || symbol;
  }

  /**
   * Validate order quantity against symbol constraints
   */
  validateOrderQuantity(symbol, quantity) {
    const metadata = this.symbolMetadata.get(symbol);
    if (!metadata) {
      return { valid: false, error: `Unknown symbol: ${symbol}` };
    }

    const numQuantity = parseFloat(quantity);
    
    if (numQuantity < metadata.minOrderSize) {
      return { 
        valid: false, 
        error: `Quantity ${quantity} below minimum ${metadata.minOrderSize} for ${symbol}` 
      };
    }
    
    if (numQuantity > metadata.maxOrderSize) {
      return { 
        valid: false, 
        error: `Quantity ${quantity} above maximum ${metadata.maxOrderSize} for ${symbol}` 
      };
    }
    
    return { valid: true };
  }

  /**
   * Format quantity according to symbol precision
   */
  formatQuantity(symbol, quantity) {
    const metadata = this.symbolMetadata.get(symbol);
    const decimals = metadata?.quantityDecimals || 8;
    return parseFloat(quantity).toFixed(decimals);
  }

  /**
   * Format price according to symbol precision
   */
  formatPrice(symbol, price) {
    const metadata = this.symbolMetadata.get(symbol);
    const decimals = metadata?.priceDecimals || 2;
    return parseFloat(price).toFixed(decimals);
  }
}

// Singleton instance
let symbolConfigInstance = null;

/**
 * Get the singleton SymbolConfig instance
 */
export function getSymbolConfig() {
  if (!symbolConfigInstance) {
    symbolConfigInstance = new SymbolConfig();
  }
  return symbolConfigInstance;
}

/**
 * Initialize symbol configuration (should be called on app startup)
 */
export async function initializeSymbolConfig() {
  const config = getSymbolConfig();
  await config.initialize();
  return config;
}
