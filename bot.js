// bot.js
import { RobinhoodAPI } from './robinhoodClient.js';
import { v4 as uuidv4 } from 'uuid';
import Big from 'big.js';
import { createStrategy } from './strategies/index.js';
import { validateConfigFile } from './config-validator.js';
import { getMultiSymbolCandleService, getCandleAggregatorService } from './services/MultiSymbolCandleService.js';
import { getTradingEventEmitter } from './services/TradingEventEmitter.js';
import { initializeSymbolConfig, getSymbolConfig } from './config/SymbolConfig.js';
import { migrateToMultiSymbol } from './config/DataMigration.js';
import fs from 'fs';
import path from 'path';

// Multi-symbol configuration - will be loaded from config or environment
let activeSymbols = []; // Will be loaded from config/environment
let symbolAllocations = {}; // Will be loaded from config
let currentSymbol = 'BTC-USD'; // Primary symbol for legacy compatibility
let botRunning = false;
let currentStopLoss = null;
let logs = [];
let tradeCount = 0;
let startingBalance = new Big(0);
let dailyStartBalance = new Big(0);
let maxTradingBalance = 0; // Start with $0 - user must explicitly provide funds
const MAX_LOGS = 1000; // Define MAX_LOGS constant

const CONFIG_FILE = 'data/bot-data.json';

// Get active symbols from environment or config (similar to server.js)
function getActiveSymbols() {
  // First try environment variable
  if (process.env.ACTIVE_SYMBOLS) {
    return process.env.ACTIVE_SYMBOLS.split(',').map(s => s.trim());
  }

  // Fallback to current activeSymbols array if already loaded
  if (activeSymbols && activeSymbols.length > 0) {
    return activeSymbols;
  }

  // Final fallback to default symbols
  return ['BTC-USD', 'ETH-USD', 'DOGE-USD', 'LTC-USD', 'BCH-USD', 'XRP-USD', 'SOL-USD'];
}

// Initialize multi-symbol candle aggregator service
const multiCandleService = getMultiSymbolCandleService();
const candleService = getCandleAggregatorService(); // Legacy compatibility

// Initialize event-driven trading system
const tradingEvents = getTradingEventEmitter();



// Strategy configuration - will be loaded from config file
// Temporary placeholder until config is loaded
let currentStrategy = null;

// Multi-symbol strategy management
let symbolStrategies = new Map(); // Map of symbol -> PortfolioManager

function log(message) {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}`;
  console.log(logEntry);
  
  logs.push(logEntry);
  
  // Keep only the last MAX_LOGS entries
  if (logs.length > MAX_LOGS) {
    logs = logs.slice(-MAX_LOGS);
  }
}

export function setStrategy(strategyName, config = {}) {
  try {
    // Create strategy for primary symbol (backward compatibility)
    currentStrategy = createStrategy(strategyName, config, currentSymbol);

    // Create strategies for all active symbols
    initializeMultiSymbolStrategies(strategyName, config);

    log(`Strategy changed to: ${currentStrategy.getName()}`);
    saveConfig(); // Save after strategy change
    return { success: true, strategy: currentStrategy.getName() };
  } catch (error) {
    log(`Failed to set strategy: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Initialize strategies for all active symbols
function initializeMultiSymbolStrategies(strategyName, config) {
  log(`🔧 Initializing ${strategyName} strategies for all symbols...`);

  // Clear existing symbol strategies
  symbolStrategies.clear();

  // Create strategy instance for each active symbol
  activeSymbols.forEach(symbol => {
    try {
      const strategy = createStrategy(strategyName, config, symbol);
      symbolStrategies.set(symbol, strategy);
      log(`✅ Created ${strategyName} strategy for ${symbol}`);
    } catch (error) {
      log(`❌ Failed to create ${strategyName} strategy for ${symbol}: ${error.message}`);
    }
  });

  log(`🎯 Multi-symbol strategies initialized for ${symbolStrategies.size} symbols`);
}

export function getCurrentStrategy() {
  return currentStrategy;
}

export function getSymbolStrategies() {
  return symbolStrategies;
}

export function getStrategyForSymbol(symbol) {
  return symbolStrategies.get(symbol);
}

export function getBotStatus() {
  return {
    running: botRunning,
    trades: tradeCount,
    logs: logs.slice(-50),
    currentStrategy: currentStrategy.getName()
  };
}

// Cache for getBotStats to reduce API calls
let botStatsCache = null;
let botStatsCacheTime = 0;
const BOT_STATS_CACHE_DURATION = 10000; // 10 seconds

// Export function to clear bot stats cache (for immediate updates after trades)
export function clearBotStatsCache() {
  botStatsCache = null;
  botStatsCacheTime = 0;
  console.log('[BOT] 🔄 Bot stats cache cleared for immediate update');
}

export async function getBotStats() {
  try {
    // Return cached stats if still fresh
    if (botStatsCache && Date.now() - botStatsCacheTime < BOT_STATS_CACHE_DURATION) {
      return botStatsCache;
    }

    const account = await RobinhoodAPI.getAccount();
    if (!account) {
      throw new Error('Failed to fetch account data');
    }

    const holdings = await RobinhoodAPI.getHoldings();

    let btcHolding = null;
    if (Array.isArray(holdings)) {
      btcHolding = holdings.find(h => h.asset_code === 'BTC');
    } else if (holdings && holdings.results && Array.isArray(holdings.results)) {
      btcHolding = holdings.results.find(h => h.asset_code === 'BTC');
    }

    // Get prices for all active symbols
    const best = await RobinhoodAPI.getBestBidAsk(activeSymbols);
    let symbolPrices = {};

    // Handle the response format according to the API documentation
    if (best && best.results && best.results.length > 0) {
      best.results.forEach(priceData => {
        const symbol = priceData.symbol;
        const price = new Big(priceData.price || priceData.ask_inclusive_of_buy_spread || '0');
        symbolPrices[symbol] = price;
      });
    }

    // Calculate multi-currency portfolio value
    const symbolConfig = getSymbolConfig();
    let totalHoldingsValue = new Big(0);
    let symbolHoldings = {};

    // Process holdings for all active symbols
    if (holdings && holdings.results && Array.isArray(holdings.results)) {
      activeSymbols.forEach(symbol => {
        const assetCode = symbolConfig.getAssetCode(symbol);
        const holding = holdings.results.find(h => h.asset_code === assetCode);
        const quantity = new Big(holding?.total_quantity || '0');
        const price = symbolPrices[symbol] || new Big(0);
        const value = quantity.mul(price);

        symbolHoldings[symbol] = {
          quantity: quantity.toFixed(8),
          price: price.toFixed(2),
          value: value.toFixed(2),
          assetCode: assetCode
        };

        totalHoldingsValue = totalHoldingsValue.add(value);
      });
    }

    // For legacy compatibility, get BTC data
    const currentPrice = symbolPrices['BTC-USD'] || new Big(0);
    const btcHoldings = new Big(btcHolding?.total_quantity ?? '0');

    const cashBalance = new Big(account.buying_power ?? account.cash?.total_available ?? '0');
    const portfolioValue = cashBalance.add(totalHoldingsValue);

    const pnl = portfolioValue.minus(dailyStartBalance.gt(0) ? dailyStartBalance : startingBalance);

    const stats = {
      botRunning,
      trades: tradeCount,
      balance: cashBalance.toFixed(2),
      // Legacy BTC fields for backward compatibility
      btcHoldings: btcHoldings.toFixed(8),
      btcPrice: currentPrice.toFixed(2),
      // Multi-currency portfolio data
      symbolHoldings: symbolHoldings,
      totalHoldingsValue: totalHoldingsValue.toFixed(2),
      portfolioValue: portfolioValue.toFixed(2),
      pnl: pnl.toFixed(2),
      stopLoss: await getStopLossInfo(),
      logs: logs.slice(-50),
      currentStrategy: currentStrategy.getName(),
      maxTradingBalance: maxTradingBalance,
      activeSymbols: activeSymbols
    };

    // Cache the stats
    botStatsCache = stats;
    botStatsCacheTime = Date.now();

    return stats;
  } catch (error) {
    log(`Error getting stats: ${error.message}`);
    return {
      botRunning,
      trades: tradeCount,
      balance: '0.00',
      btcHoldings: '0.00000000',
      btcPrice: '0.00',
      portfolioValue: '0.00',
      pnl: '0.00',
      stopLoss: { portfolioStopLoss: null, strategyStopLosses: [], totalActiveStopLosses: 0 },
      logs: logs.slice(-50),
      currentStrategy: currentStrategy.getName(),
      maxTradingBalance: maxTradingBalance,
      error: error.message
    };
  }
}

async function getStopLossInfo() {
  try {
    const stopLossInfo = {
      portfolioStopLoss: null,
      strategyStopLosses: [],
      totalActiveStopLosses: 0
    };

    // Get portfolio-wide stop loss
    if (currentStopLoss) {
      try {
        const order = await RobinhoodAPI.getOrder(currentStopLoss);
        stopLossInfo.portfolioStopLoss = {
          orderId: currentStopLoss,
          stopPrice: parseFloat(order.stop_price),
          type: 'portfolio'
        };
        stopLossInfo.totalActiveStopLosses++;
      } catch (error) {
        console.warn('Failed to get portfolio stop loss details:', error.message);
      }
    }

    // Get individual strategy stop losses
    if (currentStrategy && currentStrategy.getActiveStopLossOrders) {
      const activeOrders = currentStrategy.getActiveStopLossOrders();
      stopLossInfo.strategyStopLosses = activeOrders.map(order => ({
        strategy: order.strategy,
        orderId: order.orderId,
        stopPrice: order.stopPrice,
        timestamp: order.timestamp,
        type: 'strategy'
      }));
      stopLossInfo.totalActiveStopLosses += activeOrders.length;
    }

    return stopLossInfo;
  } catch (error) {
    console.error('Error getting stop loss info:', error);
    return {
      portfolioStopLoss: null,
      strategyStopLosses: [],
      totalActiveStopLosses: 0
    };
  }
}

export async function startBot() {
  if (botRunning) return;
  botRunning = true;
  log('[BOT] Bot started - initializing...');

  try {
    log('[BOT] 🔄 Getting account data from Robinhood...');
    const account = await RobinhoodAPI.getAccount();

    // Debug: Log the actual account response
    log(`[BOT] 🔍 Account API response:`, JSON.stringify(account, null, 2));

    if (!account) {
      throw new Error('Account API returned null/undefined - authentication may have expired');
    }

    // Handle both old and new API response formats
    let availableCash;
    if (account.cash && account.cash.total_available) {
      // Old format: account.cash.total_available
      availableCash = account.cash.total_available;
      log('[BOT] 📊 Using old API format: cash.total_available');
    } else if (account.buying_power) {
      // New format: account.buying_power
      availableCash = account.buying_power;
      log('[BOT] 📊 Using new API format: buying_power');
    } else {
      throw new Error(`Account API missing both 'cash.total_available' and 'buying_power'. Got: ${JSON.stringify(account)}`);
    }

    const usd = new Big(availableCash);
    startingBalance = usd;
    log(`[BOT] ✅ Account data retrieved: $${usd.toFixed(2)} available`);

    // Set daily start balance if not set
    if (dailyStartBalance.eq(0)) {
      log('[BOT] 🔄 Setting daily start balance...');
      const holdings = await RobinhoodAPI.getHoldings();

      // Get prices for all active symbols
      const best = await RobinhoodAPI.getBestBidAsk(activeSymbols);
      let symbolPrices = {};

      if (best && best.results && best.results.length > 0) {
        best.results.forEach(priceData => {
          const symbol = priceData.symbol;
          const price = parseFloat(priceData.price || priceData.ask_inclusive_of_buy_spread || '0');
          symbolPrices[symbol] = price;
        });
      }

      // Calculate total holdings value across all active symbols
      const symbolConfig = getSymbolConfig();
      let totalHoldingsValue = new Big(0);
      let holdingsBreakdown = {};

      if (holdings && holdings.results && Array.isArray(holdings.results)) {
        activeSymbols.forEach(symbol => {
          const assetCode = symbolConfig.getAssetCode(symbol);
          const holding = holdings.results.find(h => h.asset_code === assetCode);
          const quantity = new Big(holding?.total_quantity || '0');
          const price = new Big(symbolPrices[symbol] || '0');
          const value = quantity.mul(price);

          holdingsBreakdown[symbol] = {
            quantity: quantity.toFixed(8),
            price: price.toFixed(2),
            value: value.toFixed(2)
          };

          totalHoldingsValue = totalHoldingsValue.add(value);
        });
      }

      dailyStartBalance = usd.add(totalHoldingsValue);
      log(`[BOT] ✅ Daily start balance set: $${dailyStartBalance.toFixed(2)}`);
      log(`[BOT] 📊 Holdings breakdown:`, holdingsBreakdown);
    }

    // Validate positions against Robinhood API
    log('[BOT] 🔄 Validating positions...');
    await validatePositions();
    log('[BOT] ✅ Position validation complete');

    // Cancel any pending orders from previous session (signals no longer valid)
    if (currentStrategy && currentStrategy.constructor.name === 'PortfolioManager') {
      log('[BOT] 🔄 Cancelling stale pending orders from previous session...');
      await currentStrategy.cancelAllPendingOrdersOnStartup();
      log('[BOT] ✅ Pending order cleanup complete');
    }

    log('[BOT] 🚀 Starting main trading loop...');
    loop();
  } catch (error) {
    log(`[BOT] ❌ Failed to start bot: ${error.message}`);
    log(`[BOT] ❌ Error stack: ${error.stack}`);
    botRunning = false;
    throw error;
  }
}

export async function stopBot() {
  if (!botRunning) return;
  botRunning = false;

  // Save OHLC data before stopping
  try {
    candleService.forceSave();
    log('💾 OHLC data saved before shutdown');
  } catch (error) {
    log(`⚠️ Failed to save OHLC data on shutdown: ${error.message}`);
  }

  log('Bot stopped.');
  if (currentStopLoss) {
    await RobinhoodAPI.cancelOrder(currentStopLoss);
    log(`Canceled trailing stop-loss order: ${currentStopLoss}`);
  }
}

let priceHistory = [];
const MAX_PRICE_HISTORY = 500; // Keep last 500 price points

// Multi-symbol price tracking
let lastPriceUpdate = {};
let priceUpdateErrors = {};

// Update prices for all active symbols
async function updateAllSymbolPrices() {
  try {
    // Get all symbols that should have price updates
    const symbolsToUpdate = [...activeSymbols];

    if (symbolsToUpdate.length === 0) {
      console.log('📊 No active symbols to update');
      return;
    }

    console.log(`📊 Updating prices for ${symbolsToUpdate.length} symbols: ${symbolsToUpdate.join(', ')}`);

    // Fetch prices for all symbols in parallel
    const pricePromises = symbolsToUpdate.map(async (symbol) => {
      try {
        const best = await RobinhoodAPI.getBestBidAsk(symbol);

        let priceValue = 0;
        if (best && best.results && best.results.length > 0) {
          const priceData = best.results[0];
          priceValue = parseFloat(priceData.price || priceData.ask_inclusive_of_buy_spread || '0');
        }

        if (priceValue > 0) {
          // Update candle service for this symbol
          multiCandleService.updatePrice(symbol, priceValue);

          // Send price update event for this specific symbol to strategies
          tradingEvents.updatePrice({
            price: priceValue,
            bid: priceValue,
            ask: priceValue,
            timestamp: Date.now(),
            symbol: symbol
          });

          // Track last successful update
          lastPriceUpdate[symbol] = {
            price: priceValue,
            timestamp: Date.now()
          };

          // Clear any previous errors
          delete priceUpdateErrors[symbol];

          console.log(`📊 ${symbol}: $${priceValue.toFixed(2)}`);
          return { symbol, price: priceValue, success: true };
        } else {
          throw new Error(`No price data available for ${symbol}`);
        }
      } catch (error) {
        // Track errors but don't fail the whole update
        priceUpdateErrors[symbol] = {
          error: error.message,
          timestamp: Date.now()
        };
        console.warn(`⚠️  Failed to update ${symbol}: ${error.message}`);
        return { symbol, error: error.message, success: false };
      }
    });

    // Wait for all price updates to complete
    const results = await Promise.allSettled(pricePromises);

    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.length - successful;

    if (successful > 0) {
      console.log(`✅ Successfully updated ${successful}/${symbolsToUpdate.length} symbols`);

      // Log successful symbols for verification
      const successfulSymbols = results
        .filter(r => r.status === 'fulfilled' && r.value.success)
        .map(r => r.value.symbol);
      console.log(`📊 Updated symbols: ${successfulSymbols.join(', ')}`);
    }

    if (failed > 0) {
      console.warn(`⚠️  Failed to update ${failed}/${symbolsToUpdate.length} symbols`);

      // Log failed symbols for debugging
      const failedSymbols = results
        .filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && !r.value.success))
        .map(r => r.status === 'rejected' ? 'unknown' : r.value.symbol);
      console.warn(`❌ Failed symbols: ${failedSymbols.join(', ')}`);
    }

  } catch (error) {
    console.error('❌ Error in updateAllSymbolPrices:', error);
  }
}

async function loop() {
  log('[BOT] 🔄 Event-driven trading system started');
  while (botRunning) {
    try {
      // Update prices for all active symbols (includes primary symbol)
      await updateAllSymbolPrices();

      // Check if we have price data for any symbols (multi-currency approach)
      const availableSymbols = Object.keys(lastPriceUpdate);
      if (availableSymbols.length === 0) {
        log(`No price data available for any symbols, waiting...`);
        await delay(30000);
        continue;
      }

      // Log multi-currency portfolio status
      log(`[BOT] 📊 Multi-currency portfolio active with ${availableSymbols.length} symbols: ${availableSymbols.join(', ')}`);

      // For legacy compatibility, use BTC price for price history if available
      const btcUpdate = lastPriceUpdate['BTC-USD'];
      let currentPrice = 0;

      if (btcUpdate) {
        currentPrice = btcUpdate.price;

        // Add to price history with memory management (legacy compatibility)
        priceHistory.push(currentPrice);
        if (priceHistory.length > MAX_PRICE_HISTORY) {
          priceHistory = priceHistory.slice(-MAX_PRICE_HISTORY);
        }
      }

      // Update strategy with current price (legacy compatibility)
      if (currentStrategy && typeof currentStrategy.updatePrice === 'function' && currentPrice > 0) {
        currentStrategy.updatePrice(currentPrice);
      }

      // Get holdings for all symbols
      const holdings = await RobinhoodAPI.getHoldings();
      await delay(1000);

      // Parse holdings for all active symbols
      let symbolHoldings = {};
      const symbolConfig = getSymbolConfig();

      if (holdings && holdings.results && Array.isArray(holdings.results)) {
        activeSymbols.forEach(symbol => {
          const assetCode = symbolConfig.getAssetCode(symbol);
          const holding = holdings.results.find(h => h.asset_code === assetCode);
          symbolHoldings[symbol] = new Big(holding?.total_quantity || '0');
        });
      }

      // For legacy compatibility, get BTC holding
      const currentHolding = holdings?.results?.find(h => h.asset_code === 'BTC');
      const currentAssetQuantity = symbolHoldings['BTC-USD'] || new Big('0');

      // Get effective balance for trading
      const account = await RobinhoodAPI.getAccount();
      const usdAvailable = new Big(account.buying_power ?? account.cash?.total_available ?? '0');
      const effectiveBalance = getEffectiveBalance(parseFloat(usdAvailable.toString()));

      // Handle new trading (only if user has provided funds and we have price data)
      if (effectiveBalance > 0 && currentPrice > 0) {
        await handleMultiStrategyTrading(currentPrice, holdings, effectiveBalance);
      } else if (effectiveBalance > 0) {
        log(`[BOT] 💰 Trading funds available ($${effectiveBalance.toFixed(2)}) but waiting for price data`);
      } else {
        log(`[BOT] 💰 No trading funds available ($${effectiveBalance.toFixed(2)}) - managing existing positions only`);
      }

      // Always handle existing positions and stop losses (only if we have price data)
      if (!currentAssetQuantity.eq(0) && currentPrice > 0) {
        await handleMultiStrategyStopLosses(currentPrice, currentAssetQuantity, holdings);
      } else if (!currentAssetQuantity.eq(0)) {
        log(`[BOT] 🛡️ Holdings detected but waiting for price data to manage stop losses`);
      }

      // Shorter delay for better price update frequency - 4+ updates per minute for all symbols
      await delay(15000); // 15 seconds = 4 updates per minute
    } catch (error) {
      log(`[BOT] ❌ Error in bot loop: ${error.message}`);
      await delay(60000);
    }
  }
}

function delay(ms) {
  return new Promise((res) => setTimeout(res, ms));
}

// Handle multi-strategy trading logic
async function handleMultiStrategyTrading(currentPrice, holdings, effectiveBalance) {
  if (effectiveBalance < 5) {
    log(`[BOT] Insufficient effective balance for trading: $${effectiveBalance.toFixed(2)} (minimum $5 required)`);
    return;
  }

  // Event-driven system: Strategies will signal when they're ready to trade
  // No need to poll them - they'll emit events when conditions are met
  const readyStrategies = tradingEvents.getReadyStrategies();

  if (readyStrategies.length > 0) {
    console.log(`[BOT] 🚀 ${readyStrategies.length} strategies ready to trade:`,
      readyStrategies.map(s => s.strategyName).join(', '));

    if (currentStrategy) {
      console.log(`[BOT] 🔍 Current strategy: ${currentStrategy.getName()}`);
      console.log(`[BOT] 🔍 Current strategy type: ${currentStrategy.constructor.name}`);

      // Only PortfolioManager should handle trades via its event system
      if (currentStrategy.constructor.name !== 'PortfolioManager') {
        console.error(`[BOT] ❌ ERROR: Non-PortfolioManager strategy detected! This should not happen.`);
        console.error(`[BOT] ❌ Current strategy: ${currentStrategy.constructor.name}`);
        console.error(`[BOT] ❌ All strategies should be managed by PortfolioManager only.`);
      }
    } else {
      console.error(`[BOT] ❌ ERROR: Strategies ready but currentStrategy is null! Configuration not loaded properly.`);
    }
  } else {
    // Only log occasionally to avoid spam
    if (Math.random() < 0.1) { // 10% chance to log
      console.log(`[BOT] 📊 Event-driven system active - waiting for strategy signals (price: $${currentPrice})`);
    }
  }

  // Check if any strategies want to exit positions (still polling-based for exits)
  if (currentStrategy && currentStrategy.getStrategiesNeedingExit) {
    const exitDecisions = currentStrategy.getStrategiesNeedingExit(currentPrice);
    for (const exitDecision of exitDecisions) {
      // Get the correct price for the specific symbol being sold
      const symbolPrice = await getCurrentSymbolPrice(exitDecision.symbol);
      const priceToUse = symbolPrice || currentPrice; // Fallback to currentPrice if symbol price unavailable

      log(`[BOT] 💰 Using price $${priceToUse.toFixed(2)} for ${exitDecision.symbol} exit (strategy: ${exitDecision.strategy})`);
      await executeStrategyExit(exitDecision, priceToUse, exitDecision.symbol);
    }
  }
}

// Execute a buy order for a specific strategy and symbol
async function executeStrategyBuy(entryDecision, currentPrice, effectiveBalance, symbol = 'BTC-USD') {
  try {
    const positionSize = currentStrategy.getPositionSize(
      effectiveBalance,
      currentPrice,
      entryDecision.strategy
    );
    const qty = positionSize.quantity.toFixed(8);

    // Get asset code for display
    const symbolConfig = getSymbolConfig();
    const assetCode = symbolConfig.getAssetCode(symbol);

    log(`[BOT] ${entryDecision.reason}! Attempting to BUY ${qty} ${assetCode} at ${currentPrice.toFixed(2)} (${positionSize.reason}, max: $${effectiveBalance.toFixed(2)})`);

    const orderId = uuidv4();

    // Track pending order
    if (currentStrategy.addPendingOrder) {
      currentStrategy.addPendingOrder(entryDecision.strategy, orderId, 'market', 'buy', qty);
    }

    const res = await RobinhoodAPI.placeSimpleOrder(
      orderId,
      'buy',
      'market',
      symbol,
      { asset_quantity: qty }
    );

    if (res) {
      // Remove from pending orders when filled
      if (currentStrategy.removePendingOrder) {
        currentStrategy.removePendingOrder(entryDecision.strategy, orderId);
      }
      log(`[BOT] ✅ BUY order placed: ${qty} ${assetCode} at ${currentPrice.toFixed(2)} for strategy: ${entryDecision.strategy}`);
      tradeCount++;

      // Record trade in portfolio manager
      if (currentStrategy.recordTrade) {
        currentStrategy.recordTrade('buy', currentPrice, parseFloat(qty), entryDecision.strategy);
      }

      // Notify strategy of successful trade execution (for TestStrategy)
      notifyStrategyOfTradeExecution(entryDecision.strategy, 'buy', currentPrice, parseFloat(qty), true);
    } else {
      log('[BOT] ❌ Failed to place BUY order');

      // IMPORTANT: Remove pending order even on failure to prevent UI clutter
      if (currentStrategy.removePendingOrder) {
        currentStrategy.removePendingOrder(entryDecision.strategy, orderId);
        log(`🧹 Cleaned up failed pending order for ${entryDecision.strategy}`);
      }

      // Notify strategy of failed trade execution (for TestStrategy)
      notifyStrategyOfTradeExecution(entryDecision.strategy, 'buy', currentPrice, parseFloat(qty), false);
    }
  } catch (error) {
    log(`[BOT] ❌ Error executing buy for strategy ${entryDecision.strategy}: ${error.message}`);

    // IMPORTANT: Clean up pending order on error too
    if (currentStrategy.removePendingOrder && typeof orderId !== 'undefined') {
      currentStrategy.removePendingOrder(entryDecision.strategy, orderId);
      log(`🧹 Cleaned up pending order after error for ${entryDecision.strategy}`);
    }
  }
}

// Track strategies that are in the process of exiting to prevent stop loss conflicts
const strategiesExiting = new Set();

// Export function to check if any strategies are exiting (for debugging)
export function getExitingStrategies() {
  return Array.from(strategiesExiting);
}

// Notify strategies of trade execution results (for TestStrategy and others)
function notifyStrategyOfTradeExecution(strategyName, tradeType, price, quantity, success) {
  try {
    // Check if current strategy is a portfolio manager with strategy instances
    if (currentStrategy && currentStrategy.strategyInstances) {
      const strategy = currentStrategy.strategyInstances.get(strategyName);
      if (strategy && typeof strategy.onTradeExecuted === 'function') {
        strategy.onTradeExecuted(tradeType, price, quantity, success);

        // Handle test completion for TestStrategy
        if (strategy.constructor.name === 'TestStrategy' && success) {
          const testStatus = strategy.getTestStatus();
          if (testStatus.isCompleted) {
            handleTestCompletion(strategyName);
          }
        }
      }
    }
    // Check if current strategy is a single strategy (not portfolio manager)
    else if (currentStrategy && typeof currentStrategy.onTradeExecuted === 'function') {
      currentStrategy.onTradeExecuted(tradeType, price, quantity, success);

      // Handle test completion for TestStrategy
      if (currentStrategy.constructor.name === 'TestStrategy' && success) {
        const testStatus = currentStrategy.getTestStatus();
        if (testStatus.isCompleted) {
          handleTestCompletion('test');
        }
      }
    }
  } catch (error) {
    log(`[BOT] ❌ Error notifying strategy ${strategyName} of trade execution: ${error.message}`);
  }
}

// Handle test completion by setting flag in config
function handleTestCompletion(strategyName) {
  try {
    log(`[BOT] 🎉 Test strategy '${strategyName}' completed successfully! Setting completion flag.`);

    // Load current config
    let config = {};
    if (fs.existsSync(CONFIG_FILE)) {
      const configData = fs.readFileSync(CONFIG_FILE, 'utf8');
      config = JSON.parse(configData);
    }

    // Set test completion flag
    if (!config.testResults) {
      config.testResults = {};
    }
    config.testResults.buyAndSellTestCompleted = true;
    config.testResults.completedAt = new Date().toISOString();
    config.testResults.strategyName = strategyName;

    // Save updated config
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
    log(`[BOT] ✅ Test completion flag saved to config file`);

  } catch (error) {
    log(`[BOT] ❌ Error saving test completion flag: ${error.message}`);
  }
}

// Execute a sell order for a specific strategy and symbol
async function executeStrategyExit(exitDecision, currentPrice, symbol = 'BTC-USD') {
  try {
    // Mark strategy as exiting to prevent stop loss recreation
    strategiesExiting.add(exitDecision.strategy);
    log(`[BOT] 🔒 Marking ${exitDecision.strategy} as exiting - preventing stop loss recreation`);

    // Final safety check: Verify strategy actually has a position
    if (currentStrategy.getStrategyPosition) {
      const strategyPosition = currentStrategy.getStrategyPosition(exitDecision.strategy);
      if (!strategyPosition || strategyPosition.quantity <= 0) {
        log(`[BOT] 🚫 SAFETY BLOCK: Strategy ${exitDecision.strategy} attempted to sell but has no position (${strategyPosition?.quantity || 0} BTC)`);
        strategiesExiting.delete(exitDecision.strategy); // Clean up exit flag
        return;
      }

      // Ensure we don't sell more than the strategy owns
      const maxSellable = strategyPosition.quantity;
      if (exitDecision.quantityToSell > maxSellable) {
        log(`[BOT] 🚫 SAFETY BLOCK: Strategy ${exitDecision.strategy} attempted to sell ${exitDecision.quantityToSell.toFixed(8)} BTC but only owns ${maxSellable.toFixed(8)} BTC`);
        exitDecision.quantityToSell = maxSellable;
      }
    }

    // Check for open orders that might lock BTC (same logic as stop loss)
    log(`[BOT] 🔍 Checking for open orders before selling ${exitDecision.quantityToSell.toFixed(8)} BTC...`);

    let lockedBtc = 0;
    let openOrders = null;
    try {
      openOrders = await RobinhoodAPI.getOpenOrders(currentSymbol);

      if (openOrders && openOrders.results && openOrders.results.length > 0) {
        log(`[BOT] ⚠️  Found ${openOrders.results.length} open BTC orders:`);
        openOrders.results.forEach(order => {
          const quantity = order.asset_quantity || order.notional_amount || 'unknown';
          log(`  📋 Order ${order.id}: ${order.side} ${order.type} ${quantity} BTC (${order.state})`);

          // Count BTC locked in ALL open sell orders (including stop losses)
          if (order.side === 'sell' && (order.state === 'open' || order.state === 'confirmed' || order.state === 'queued')) {
            let orderQuantity = 0;

            if (order.asset_quantity) {
              orderQuantity = parseFloat(order.asset_quantity);
            } else if (order.type === 'stop_loss') {
              // For stop loss orders without asset_quantity, assume they lock the strategy's full position
              // This is a safe assumption since stop losses typically cover the entire position
              orderQuantity = exitDecision.quantityToSell;
              log(`  🔍 Stop loss order ${order.id} has no asset_quantity, assuming it locks ${orderQuantity.toFixed(8)} BTC`);
            }

            if (orderQuantity > 0) {
              lockedBtc += orderQuantity;
              log(`  🔒 Locking ${orderQuantity.toFixed(8)} BTC from ${order.type} order`);
            }
          }
        });

        if (lockedBtc > 0) {
          log(`[BOT] 🔒 Total ${lockedBtc.toFixed(8)} BTC locked in open sell orders`);
        }
      } else {
        log('[BOT] ✅ No open BTC orders found');
      }
    } catch (error) {
      log(`[BOT] ⚠️  Could not check open orders: ${error.message} - proceeding with sell attempt`);
    }

    // Get actual BTC holdings from Robinhood
    let actualBtcQuantity = 0;
    try {
      const holdings = await RobinhoodAPI.getHoldings();
      log(`[BOT] 🔍 DEBUG: Holdings response type: ${typeof holdings}, isArray: ${Array.isArray(holdings)}`);

      let btcHolding = null;

      const symbolConfig = getSymbolConfig();
      const assetCode = symbolConfig.getAssetCode(currentSymbol);

      if (Array.isArray(holdings)) {
        // Holdings is an array
        btcHolding = holdings.find(h => h.symbol === currentSymbol);
      } else if (holdings && holdings.results && Array.isArray(holdings.results)) {
        // Holdings has a results array
        btcHolding = holdings.results.find(h => h.symbol === currentSymbol);
      } else if (holdings && typeof holdings === 'object') {
        // Holdings might be a direct object or have other structure
        log(`[BOT] 🔍 DEBUG: Holdings structure:`, Object.keys(holdings));
        // Try to find BTC in various possible structures
        if (holdings['BTC-USD']) {
          btcHolding = holdings['BTC-USD'];
        } else if (holdings.BTC) {
          btcHolding = holdings.BTC;
        }
      }

      actualBtcQuantity = btcHolding ? parseFloat(btcHolding.quantity || btcHolding.amount || 0) : 0;
      log(`[BOT] 📊 Actual BTC holdings: ${actualBtcQuantity.toFixed(8)} BTC`);
    } catch (error) {
      log(`[BOT] ⚠️  Could not get holdings: ${error.message} - using tracked quantity`);
      actualBtcQuantity = exitDecision.quantityToSell; // Fallback to what we think we have
    }

    // Calculate available BTC (actual - locked in orders)
    const availableBtc = actualBtcQuantity - lockedBtc;
    log(`[BOT] 💡 Available BTC for sale: ${availableBtc.toFixed(8)} (${actualBtcQuantity.toFixed(8)} total - ${lockedBtc.toFixed(8)} locked)`);

    // Use the smaller of requested vs available to avoid "Not enough BTC" errors
    const safeQuantity = Math.min(exitDecision.quantityToSell, availableBtc);

    if (safeQuantity < 0.00001) {
      log(`[BOT] ⚠️  No available BTC to sell for ${exitDecision.strategy} - all BTC may be locked in open orders`);

      // If we found open orders (especially stop losses), try to cancel conflicting ones
      // This handles cases where lockedBtc calculation might be inaccurate due to missing asset_quantity
      if (openOrders && openOrders.results && openOrders.results.length > 0) {
        const hasStopLossOrders = openOrders.results.some(order =>
          order.side === 'sell' && order.type === 'stop_loss' &&
          (order.state === 'open' || order.state === 'confirmed')
        );

        if (hasStopLossOrders || lockedBtc > 0) {
          log(`[BOT] 🔧 Attempting to cancel conflicting stop loss orders to free up BTC...`);

          for (const order of openOrders.results) {
            if (order.side === 'sell' && order.type === 'stop_loss' &&
                (order.state === 'open' || order.state === 'confirmed')) {
              try {
                log(`[BOT] 🚫 Cancelling stop loss order ${order.id} to free up BTC for ${exitDecision.strategy}`);
                const cancelResult = await RobinhoodAPI.cancelOrder(order.id);

                if (cancelResult) {
                  log(`[BOT] ✅ Successfully cancelled stop loss ${order.id}`);

                  // Wait a moment for the cancellation to process, then retry the sell
                  await new Promise(resolve => setTimeout(resolve, 2000));

                  // Recursively retry the sell now that BTC should be available
                  log(`[BOT] 🔄 Retrying sell for ${exitDecision.strategy} after stop loss cancellation`);
                  return await executeStrategyExit(exitDecision, currentPrice, symbol);

                } else {
                  log(`[BOT] ⚠️  Failed to cancel stop loss ${order.id}`);
                }
              } catch (error) {
                log(`[BOT] ❌ Error cancelling stop loss ${order.id}: ${error.message}`);
              }
            }
          }
        }
      }

      return;
    }

    // Get asset code for display
    const symbolConfig = getSymbolConfig();
    const assetCode = symbolConfig.getAssetCode(symbol);

    if (safeQuantity < exitDecision.quantityToSell) {
      log(`[BOT] ⚠️  Reducing sell quantity from ${exitDecision.quantityToSell.toFixed(8)} to ${safeQuantity.toFixed(8)} ${assetCode} due to locked orders`);
    }

    const qty = safeQuantity.toFixed(8);

    log(`${exitDecision.decision.reason}! Attempting to SELL ${qty} ${assetCode} at ${currentPrice.toFixed(2)} for strategy: ${exitDecision.strategy}`);

    const orderId = uuidv4();

    // Track pending order
    if (currentStrategy.addPendingOrder) {
      currentStrategy.addPendingOrder(exitDecision.strategy, orderId, 'market', 'sell', qty);
    }

    const res = await RobinhoodAPI.placeSimpleOrder(
      orderId,
      'sell',
      'market',
      symbol,
      { asset_quantity: qty }
    );

    if (res) {
      // Remove from pending orders when filled
      if (currentStrategy.removePendingOrder) {
        currentStrategy.removePendingOrder(exitDecision.strategy, orderId);
      }

      // Get asset code for display
      const symbolConfig = getSymbolConfig();
      const assetCode = symbolConfig.getAssetCode(symbol);
      log(`SELL order placed: ${qty} ${assetCode} at ${currentPrice.toFixed(2)} for strategy: ${exitDecision.strategy}`);
      tradeCount++;

      // Record trade in portfolio manager
      if (currentStrategy.recordTrade) {
        currentStrategy.recordTrade('sell', currentPrice, parseFloat(qty), exitDecision.strategy, symbol);
      }

      // Notify strategy of successful trade execution (for TestStrategy)
      notifyStrategyOfTradeExecution(exitDecision.strategy, 'sell', currentPrice, parseFloat(qty), true);
    } else {
      log('❌ Failed to place SELL order');

      // IMPORTANT: Remove pending order even on failure to prevent UI clutter
      if (currentStrategy.removePendingOrder) {
        currentStrategy.removePendingOrder(exitDecision.strategy, orderId);
        log(`🧹 Cleaned up failed pending order for ${exitDecision.strategy}`);
      }

      // Notify strategy of failed trade execution (for TestStrategy)
      notifyStrategyOfTradeExecution(exitDecision.strategy, 'sell', currentPrice, parseFloat(qty), false);
    }

    // Always clean up exit flag when done (success or failure)
    strategiesExiting.delete(exitDecision.strategy);
    log(`[BOT] 🔓 ${exitDecision.strategy} exit complete - allowing stop loss recreation`);

  } catch (error) {
    log(`Error executing sell for strategy ${exitDecision.strategy}: ${error.message}`);

    // IMPORTANT: Clean up pending order on error too
    if (currentStrategy.removePendingOrder && typeof orderId !== 'undefined') {
      currentStrategy.removePendingOrder(exitDecision.strategy, orderId);
      log(`🧹 Cleaned up pending order after error for ${exitDecision.strategy}`);
    }

    // Always clean up exit flag on error too
    strategiesExiting.delete(exitDecision.strategy);
    log(`[BOT] 🔓 ${exitDecision.strategy} exit failed - allowing stop loss recreation`);
  }
}

// Check if stop loss order needs updating (avoid unnecessary churn)
async function shouldUpdateStopLossOrder(newStopPrice, newQuantity, symbol = 'BTC-USD') {
  try {
    // Get current open stop loss orders for the specified symbol
    const openOrders = await RobinhoodAPI.getOpenOrders(symbol);
    if (!openOrders || !openOrders.results) {
      return true; // No orders found, need to create one
    }

    const stopLossOrders = openOrders.results.filter(order =>
      order.type === 'stop_loss' && order.side === 'sell' && order.state === 'open'
    );

    if (stopLossOrders.length === 0) {
      log('📊 No existing stop loss orders found - need to create one');
      return true; // No stop loss exists, need to create one
    }

    if (stopLossOrders.length > 1) {
      log(`⚠️  Multiple stop loss orders found (${stopLossOrders.length}) - need to clean up`);
      return true; // Multiple orders, need to clean up
    }

    // Check the existing stop loss order
    const existingOrder = stopLossOrders[0];
    const existingStopPrice = parseFloat(existingOrder.stop_price || 0);
    const existingQuantity = parseFloat(existingOrder.asset_quantity || 0);
    const newStopPriceNum = parseFloat(newStopPrice);
    const newQuantityNum = parseFloat(newQuantity);

    // Calculate differences
    const priceDifference = Math.abs(existingStopPrice - newStopPriceNum);
    const quantityDifference = Math.abs(existingQuantity - newQuantityNum);

    // Thresholds for updates
    const priceThreshold = newStopPriceNum * 0.005; // 0.5% price change
    const quantityThreshold = 0.00001; // 1 satoshi quantity change

    log(`📊 Stop loss comparison:`);
    log(`  Current: $${existingStopPrice.toFixed(2)} for ${existingQuantity.toFixed(8)} BTC`);
    log(`  New: $${newStopPriceNum.toFixed(2)} for ${newQuantityNum.toFixed(8)} BTC`);
    log(`  Price diff: $${priceDifference.toFixed(2)} (threshold: $${priceThreshold.toFixed(2)})`);
    log(`  Quantity diff: ${quantityDifference.toFixed(8)} BTC (threshold: ${quantityThreshold.toFixed(8)})`);

    // CRITICAL: Only allow stop loss to move UP (trailing stop protection)
    // Never allow stop loss to be lowered as this increases potential loss
    if (newStopPriceNum <= existingStopPrice) {
      log(`🛡️  Stop loss protection: new price $${newStopPriceNum.toFixed(2)} <= existing $${existingStopPrice.toFixed(2)} - blocking downward move`);
      return false; // Block any attempt to lower stop loss
    }

    // Update if significant upward change in price or quantity change
    if (priceDifference > priceThreshold || quantityDifference > quantityThreshold) {
      log(`🔄 Stop loss needs update: upward move from $${existingStopPrice.toFixed(2)} to $${newStopPriceNum.toFixed(2)}`);
      return true;
    }

    log(`✅ Stop loss is current: no significant upward change (trailing stop protection active)`);
    return false;

  } catch (error) {
    log(`❌ Error checking stop loss status: ${error.message}`);
    return true; // On error, assume update is needed
  }
}

// Handle stop losses for multiple strategies
async function handleMultiStrategyStopLosses(currentPrice, totalBtc, holdings, symbol = 'BTC-USD') {
  // Note: Due to Robinhood API limitations, we use a single stop loss for the total position
  // In a more advanced implementation, each strategy would have its own stop loss orders

  // Throttle stop loss checks to avoid excessive API calls (max once per 5 minutes)
  const now = Date.now();
  const STOP_LOSS_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes
  if (now - (global.lastStopLossCheck || 0) < STOP_LOSS_CHECK_INTERVAL) {
    // log(`⏳ Stop loss check throttled (last check ${Math.round((now - global.lastStopLossCheck) / 1000)}s ago)`);
    return;
  }
  global.lastStopLossCheck = now;

  // Cancel existing stop loss
  if (currentStopLoss) {
    log(`🚫 Cancelling tracked stop loss: ${currentStopLoss}`);
    await RobinhoodAPI.cancelOrder(currentStopLoss);
    await delay(1000);
  }

  // Also cancel any other open stop loss orders that might exist
  log('🔍 Checking for any other open stop loss orders...');
  const openOrders = await RobinhoodAPI.getOpenOrders(symbol);
  if (openOrders && openOrders.results && openOrders.results.length > 0) {
    const stopLossOrders = openOrders.results.filter(order =>
      order.type === 'stop_loss' && order.side === 'sell' && order.state === 'open'
    );

    if (stopLossOrders.length > 0) {
      log(`🚫 Found ${stopLossOrders.length} existing stop loss orders to cancel:`);
      for (const order of stopLossOrders) {
        log(`  🚫 Cancelling stop loss order: ${order.id}`);
        await RobinhoodAPI.cancelOrder(order.id);
        await delay(500); // Small delay between cancellations
      }
      log('✅ All existing stop loss orders cancelled');
      await delay(2000); // Wait for cancellations to process
    } else {
      log('✅ No existing stop loss orders found');
    }
  }

  // Get stop loss decisions from all strategies with positions
  const stopLossDecisions = [];

  if (currentStrategy.strategyInstances) {
    currentStrategy.strategyInstances.forEach((strategy, name) => {
      // Skip stop loss creation if strategy is in the process of exiting
      if (strategiesExiting.has(name)) {
        log(`[BOT] ⏸️  Skipping stop loss for ${name} - strategy is exiting`);
        return;
      }

      const position = currentStrategy.getStrategyPosition(name);
      if (position.quantity > 0) {
        const stopLoss = currentStrategy.calculateStrategyStopLoss(name, currentPrice, { holdings });
        if (stopLoss) {
          stopLossDecisions.push(stopLoss);
        }
      }
    });
  }

  if (stopLossDecisions.length > 0) {
    // Calculate weighted average stop price based on position sizes
    let totalValue = 0;
    let weightedStopPrice = 0;

    stopLossDecisions.forEach(decision => {
      const positionValue = decision.quantity * currentPrice;
      totalValue += positionValue;
      weightedStopPrice += decision.stopPrice * positionValue;
    });

    const avgStopPrice = totalValue > 0 ? (weightedStopPrice / totalValue) : currentPrice * 0.95;
    const stopPrice = avgStopPrice.toFixed(2);

    // Check if we need to update the stop loss (avoid unnecessary updates)
    const shouldUpdateStopLoss = await shouldUpdateStopLossOrder(stopPrice, totalBtc);
    if (!shouldUpdateStopLoss) {
      log(`✅ Stop loss already optimal at $${stopPrice} for ${totalBtc.toFixed(8)} BTC - no update needed`);
      return;
    }

    log(`🔄 Stop loss needs update: new price $${stopPrice} for ${totalBtc.toFixed(8)} BTC`);

    // Get actual BTC holdings before placing stop loss to avoid mismatch
    log('🔍 Verifying actual BTC holdings before placing stop loss...');
    const actualHoldings = await RobinhoodAPI.getHoldings();
    let actualBtcQuantity = 0;

    if (Array.isArray(actualHoldings)) {
      const btcHolding = actualHoldings.find(h => h.asset_code === 'BTC');
      actualBtcQuantity = btcHolding ? parseFloat(btcHolding.total_quantity || btcHolding.quantity || '0') : 0;
    } else if (actualHoldings && actualHoldings.results && Array.isArray(actualHoldings.results)) {
      const btcHolding = actualHoldings.results.find(h => h.asset_code === 'BTC');
      actualBtcQuantity = btcHolding ? parseFloat(btcHolding.total_quantity || btcHolding.quantity || '0') : 0;
    }

    log(`📊 Tracked BTC: ${totalBtc.toFixed(8)}, Actual BTC: ${actualBtcQuantity.toFixed(8)}`);

    // Check for open orders that might be locking assets
    const symbolConfig = getSymbolConfig();
    const assetCode = symbolConfig.getAssetCode(symbol);
    log(`🔍 Checking for open orders that might lock ${assetCode}...`);
    const openOrders = await RobinhoodAPI.getOpenOrders(symbol);
    let lockedBtc = 0;

    if (openOrders && openOrders.results && openOrders.results.length > 0) {
      log(`⚠️  Found ${openOrders.results.length} open BTC orders:`);
      openOrders.results.forEach(order => {
        log(`  📋 Order ${order.id}: ${order.side} ${order.type} ${order.asset_quantity || order.notional_amount} BTC (${order.state})`);

        // Count BTC locked in open sell orders
        if (order.side === 'sell' && order.asset_quantity) {
          lockedBtc += parseFloat(order.asset_quantity);
        }
      });

      if (lockedBtc > 0) {
        log(`🔒 ${lockedBtc.toFixed(8)} BTC locked in open sell orders`);
      }
    } else {
      log('✅ No open BTC orders found');
    }

    // Calculate available BTC (actual - locked in orders)
    const availableBtc = actualBtcQuantity - lockedBtc;
    log(`💡 Available BTC for stop loss: ${availableBtc.toFixed(8)} (${actualBtcQuantity.toFixed(8)} total - ${lockedBtc.toFixed(8)} locked)`);

    // Use the smaller of tracked vs available to avoid "Not enough BTC" errors
    const safeQuantity = Math.min(totalBtc, availableBtc);

    if (safeQuantity < 0.00001) {
      log('⚠️  No available BTC to place stop loss for - skipping');
      return;
    }

    log(`Placing portfolio stop: ${safeQuantity.toFixed(8)} BTC at ${stopPrice} (weighted average from ${stopLossDecisions.length} strategies)`);

    // For now, place stop-loss for BTC (legacy compatibility)
    // TODO: Implement per-symbol stop-loss logic
    const stopSymbol = 'BTC-USD';

    const res = await RobinhoodAPI.placeOrder({
      symbol: stopSymbol,
      clientOrderId: uuidv4(),
      side: 'sell',
      type: 'stop_loss',
      config: {
        stop_price: stopPrice,
        asset_quantity: safeQuantity.toFixed(8),
        time_in_force: 'gtc'  // Good Till Cancelled
      }
    });

    if (res && res.id) {
      currentStopLoss = res.id;
      log(`Portfolio stop loss placed at ${stopPrice} (Order ID: ${currentStopLoss})`);

      // Track the stop loss in portfolio manager
      if (currentStrategy.updateStrategyStopLoss) {
        // For now, assign to the strategy with the largest position
        const largestStrategy = stopLossDecisions.reduce((max, current) =>
          current.quantity > max.quantity ? current : max
        );
        currentStrategy.updateStrategyStopLoss(largestStrategy.strategy, res.id, parseFloat(stopPrice));
      }
    } else {
      log('Failed to place portfolio stop loss order - likely position mismatch');

      // When stop loss fails, it's usually due to position mismatch
      // Trigger position sync to fix tracking
      log('⚠️  Triggering position sync to fix tracking mismatch...');
      if (currentStrategy && currentStrategy.checkAndUpdateFilledOrders) {
        currentStrategy.checkAndUpdateFilledOrders().catch(syncError => {
          log(`Position sync failed: ${syncError.message}`);
        });
      }
    }
  }
}

// Get current price for a specific symbol
async function getCurrentSymbolPrice(symbol) {
  try {
    // Try to get price from Robinhood API
    const priceData = await RobinhoodAPI.getPrice(symbol);
    if (priceData && priceData.price) {
      const price = parseFloat(priceData.price);
      log(`[BOT] 📊 Got current price for ${symbol}: $${price.toFixed(2)}`);
      return price;
    }
  } catch (error) {
    log(`[BOT] ⚠️  Failed to get current price for ${symbol}: ${error.message}`);
  }

  // Fallback: try to get from candle aggregator if available
  try {
    if (candleAggregator && candleAggregator.getLatestPrice) {
      const price = candleAggregator.getLatestPrice(symbol);
      if (price && price > 0) {
        log(`[BOT] 📊 Got price from candle aggregator for ${symbol}: $${price.toFixed(2)}`);
        return price;
      }
    }
  } catch (error) {
    log(`[BOT] ⚠️  Failed to get price from candle aggregator for ${symbol}: ${error.message}`);
  }

  log(`[BOT] ❌ Could not get current price for ${symbol} - will use fallback`);
  return null;
}

// Technical analysis helpers
function calculateSMA(prices, period) {
  if (prices.length < period) return null;
  const sum = prices.slice(-period).reduce((a, b) => a + b, 0);
  return sum / period;
}

function calculateATR(prices, period = 14) {
  if (prices.length < period + 1) return BASE_STOP_LOSS_PERCENT;
  
  let atr = 0;
  for (let i = prices.length - period; i < prices.length - 1; i++) {
    const high = Math.max(prices[i], prices[i + 1]);
    const low = Math.min(prices[i], prices[i + 1]);
    const trueRange = high - low;
    atr += trueRange;
  }
  
  const avgTrueRange = atr / period;
  const currentPrice = prices[prices.length - 1];
  
  // Convert ATR to percentage and cap it
  const atrPercent = (avgTrueRange / currentPrice) * 2; // 2x ATR for stop
  return Math.min(Math.max(atrPercent, 0.015), 0.08); // Between 1.5% and 8%
}

function isUptrend(prices, period = MIN_TREND_LOOKBACK) {
  if (prices.length < period) return true; // Default to true if not enough data
  
  const sma = calculateSMA(prices, period);
  const currentPrice = prices[prices.length - 1];
  
  // Price above SMA and SMA is rising
  const olderSma = calculateSMA(prices.slice(0, -5), period);
  return currentPrice > sma && sma > olderSma;
}

function shouldEnterTrade(currentPrice) {
  if (priceHistory.length < MIN_TREND_LOOKBACK) {
    log('Not enough price history for analysis, waiting...');
    return false;
  }
  
  // Check if we're in an uptrend
  if (!isUptrend(priceHistory)) {
    log('Market not in uptrend, waiting for better conditions');
    return false;
  }
  
  // Check if price is above short-term moving average
  const shortSMA = calculateSMA(priceHistory, 5);
  if (currentPrice < shortSMA) {
    log('Price below short-term average, waiting for pullback to end');
    return false;
  }
  
  // Additional filter: avoid buying at recent highs
  const recentHigh = Math.max(...priceHistory.slice(-10));
  if (currentPrice > recentHigh * 0.98) {
    log('Price too close to recent high, waiting for better entry');
    return false;
  }
  
  return true;
}

export function setMaxTradingBalance(amount) {
  try {
    const oldMaxBalance = maxTradingBalance;

    // Handle null or undefined inputs by defaulting to 0
    if (amount === null || amount === undefined) {
      maxTradingBalance = 0;
    } else {
      maxTradingBalance = parseFloat(amount);
      // If parseFloat returns NaN, default to 0
      if (isNaN(maxTradingBalance)) {
        maxTradingBalance = 0;
      }
    }

    log(`Max trading balance set to: $${maxTradingBalance.toFixed(2)}`);

    // Handle position reconciliation if balance is reduced
    if (botRunning && currentStrategy.reconcilePositionsForBalanceChange) {
      handleMaxBalanceChange(oldMaxBalance, maxTradingBalance);
    }

    saveConfig(); // Save after balance change
    return { success: true, maxBalance: maxTradingBalance };
  } catch (error) {
    log(`Failed to set max trading balance: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Update symbol allocations (called by server when user changes sliders)
export function updateSymbolAllocations(newAllocations) {
  try {
    symbolAllocations = newAllocations;
    log(`📊 Symbol allocations updated: ${JSON.stringify(symbolAllocations)}`);

    // Update PortfolioManager if it exists
    if (currentStrategy && currentStrategy.symbolAllocations !== undefined) {
      currentStrategy.symbolAllocations = newAllocations;
      log(`📊 PortfolioManager symbol allocations updated`);
    }

    return { success: true, symbolAllocations };
  } catch (error) {
    log(`Failed to update symbol allocations: ${error.message}`);
    return { success: false, error: error.message };
  }
}

export function getMaxTradingBalance() {
  return maxTradingBalance;
}

// Export candle service for API access
export function getCandleService() {
  return candleService;
}

function getEffectiveBalance(availableBalance) {
  // If maxTradingBalance is 0 or null, no new trades allowed (but can manage existing positions)
  if (maxTradingBalance === 0 || maxTradingBalance === null) {
    return 0;
  }
  return Math.min(availableBalance, maxTradingBalance);
}

// Handle max trading balance changes and position reconciliation
async function handleMaxBalanceChange(oldMaxBalance, newMaxBalance) {
  try {
    // Get current price for calculations
    const best = await RobinhoodAPI.getBestBidAsk(currentSymbol);

    let currentPrice = 0;
    if (best && best.results && best.results.length > 0) {
      const priceData = best.results[0];
      currentPrice = parseFloat(priceData.price || priceData.ask_inclusive_of_buy_spread || '0');
    }

    if (currentPrice === 0) {
      log('Cannot reconcile positions: no current price available');
      return;
    }

    // Get reconciliation plan from portfolio manager
    const reconciliationPlan = currentStrategy.reconcilePositionsForBalanceChange(newMaxBalance, currentPrice);

    if (reconciliationPlan.action === 'none') {
      log(reconciliationPlan.message);
      return;
    }

    if (reconciliationPlan.action === 'error') {
      log(`Position reconciliation error: ${reconciliationPlan.message}`);
      return;
    }

    if (reconciliationPlan.action === 'reduce') {
      log(`Position reconciliation needed: ${reconciliationPlan.message}`);

      // Execute reduction plan
      for (const reduction of reconciliationPlan.reductionPlan) {
        if (reduction.quantityToSell > 0.00001) {
          const assetCode = reduction.symbol ? reduction.symbol.split('-')[0] : 'BTC';
          const symbol = reduction.symbol || currentSymbol;

          // Apply proper precision formatting for sell orders
          const robinhoodPrecision = {
            'BTC-USD': 8,  // 0.00000001
            'ETH-USD': 6,  // 0.000001
            'DOGE-USD': 2, // 0.01
            'LTC-USD': 6,  // 0.000001
            'BCH-USD': 6,  // 0.000001
            'XRP-USD': 2,  // 0.01
            'SOL-USD': 4   // 0.0001
          };

          const requiredDecimals = robinhoodPrecision[symbol] || 6;
          const roundedQuantity = Math.round(reduction.quantityToSell * Math.pow(10, requiredDecimals)) / Math.pow(10, requiredDecimals);
          const formattedQuantity = roundedQuantity.toFixed(requiredDecimals);

          log(`Reducing ${reduction.strategy} ${symbol} position: selling ${formattedQuantity} ${assetCode} (${reduction.reason})`);

          const res = await RobinhoodAPI.placeOrder({
            symbol: symbol,
            clientOrderId: uuidv4(),
            side: 'sell',
            type: 'market',
            config: { asset_quantity: formattedQuantity }
          });

          if (res) {
            log(`Reduction order placed: ${reduction.quantityToSell.toFixed(8)} ${assetCode} for ${reduction.strategy}`);
            tradeCount++;

            // Record the trade with correct symbol price
            if (currentStrategy.recordTrade) {
              // Get the correct price for this symbol
              const symbolPrice = await getCurrentSymbolPrice(symbol);
              const priceToRecord = symbolPrice || currentPrice; // Fallback to currentPrice if unavailable
              log(`[BOT] 📝 Recording reduction trade for ${symbol} at $${priceToRecord.toFixed(2)}`);
              currentStrategy.recordTrade('sell', priceToRecord, reduction.quantityToSell, reduction.strategy, symbol);
            }

            // Small delay between orders
            await delay(2000);
          } else {
            log(`Failed to place reduction order for ${reduction.strategy} ${symbol}`);
          }
        }
      }

      const totalReduction = reconciliationPlan.totalReduction;
      if (totalReduction && !isNaN(totalReduction)) {
        log(`Position reconciliation completed. Total reduction: $${totalReduction.toFixed(2)}`);
      } else {
        log(`Position reconciliation completed. Total reduction: $0.00 (calculation error)`);
      }
    }
  } catch (error) {
    log(`Error during position reconciliation: ${error.message}`);
  }
}

// Load saved configuration on startup with validation
async function loadConfig() {
  try {
    log('🔍 Initializing symbol configuration...');

    // Initialize symbol configuration first
    await initializeSymbolConfig();

    log('🔍 Checking for data migration...');

    // Check if migration is needed and run it
    const migrationResult = await migrateToMultiSymbol(CONFIG_FILE);
    if (migrationResult.migrated) {
      log('✅ Data migrated to multi-symbol structure');
    }

    log('🔍 Validating and loading configuration...');

    // Validate and fix config file
    const validationResult = await validateConfigFile(CONFIG_FILE);

    if (!validationResult.isValid) {
      log('⚠️ Configuration had errors, but fixes were applied');
    }

    if (validationResult.fixes.length > 0) {
      log(`🔧 Applied ${validationResult.fixes.length} configuration fixes`);
      validationResult.fixes.forEach(fix => {
        log(`  ${fix.strategy}: ${fix.fixes.length} fixes applied`);
      });
    }

    const config = validationResult.config;

    // Load multi-symbol configuration
    if (config.activeSymbols && Array.isArray(config.activeSymbols)) {
      activeSymbols = config.activeSymbols;
      log(`📊 Loaded active symbols from config: ${activeSymbols.join(', ')}`);
    } else {
      // Use environment or default symbols
      activeSymbols = getActiveSymbols();
      log(`📊 Using active symbols from environment/default: ${activeSymbols.join(', ')}`);
    }

    if (config.symbolAllocations && typeof config.symbolAllocations === 'object') {
      symbolAllocations = config.symbolAllocations;
      log(`📊 Loaded symbol allocations: ${JSON.stringify(symbolAllocations)}`);
    } else {
      // Create default equal allocations
      const allocation = 1.0 / activeSymbols.length;
      symbolAllocations = {};
      activeSymbols.forEach(symbol => {
        symbolAllocations[symbol] = allocation;
      });
      log(`📊 Created default symbol allocations: ${JSON.stringify(symbolAllocations)}`);
    }

    // Set primary symbol (first active symbol or BTC-USD)
    currentSymbol = activeSymbols[0] || 'BTC-USD';

    // Restore max trading balance (default to $0 if not set or null)
    if (config.maxTradingBalance !== undefined && config.maxTradingBalance !== null) {
      maxTradingBalance = config.maxTradingBalance;
    } else {
      maxTradingBalance = 0; // Default to $0 - user must provide funds
    }
    log(`Restored max trading balance: $${maxTradingBalance?.toFixed(2) || '0.00'} (user must provide funds for new trades)`);

    // Restore portfolio configuration
    if (config.portfolio && config.portfolio.strategies.length > 0) {
      try {
        // Clean up old strategy before creating new one
        if (currentStrategy && currentStrategy.cleanup) {
          log('🧹 Cleaning up old strategy before loading config...');
          currentStrategy.cleanup();
        }

        currentStrategy = createStrategy('portfolio-manager', config.portfolio, currentSymbol);
        log(`✅ Restored portfolio with ${config.portfolio.strategies.length} strategies:`);
        config.portfolio.strategies.forEach(s => {
          log(`  - ${s.name}: ${(s.allocation * 100).toFixed(1)}% allocation ${s.enabled === false ? '(disabled)' : ''}`);
        });
        log(`✅ Current strategy set to: ${currentStrategy.getName()} (${currentStrategy.constructor.name})`);

        // Restore positions and trade history
        if (config.positions) {
          restorePositions(config.positions);
        }
        if (config.tradeHistory) {
          restoreTradeHistory(config.tradeHistory);
        }
        // Restore unaccounted holdings (crypto not purchased by bot)
        if (config.unaccountedHoldings) {
          currentStrategy.unaccountedHoldings = config.unaccountedHoldings;
          const symbolCount = Object.keys(config.unaccountedHoldings).length;
          if (symbolCount > 0) {
            log(`📝 Restored ${symbolCount} unaccounted crypto holdings (not managed by bot)`);
          }
        }
      } catch (error) {
        log(`Failed to restore portfolio: ${error.message}, using default`);
        await createDefaultPortfolio();
      }
    }
    // Legacy support for old config format
    else if (config.strategy && config.strategy.name === 'portfolio-manager') {
      try {
        currentStrategy = createStrategy('portfolio-manager', config.strategy.config, currentSymbol);

        // Initialize strategies for all active symbols
        initializeMultiSymbolStrategies('portfolio-manager', config.strategy.config);

        log(`Restored legacy portfolio configuration`);
        log(`🎯 Multi-symbol strategies active for: ${Array.from(symbolStrategies.keys()).join(', ')}`);
      } catch (error) {
        log(`❌ Failed to restore legacy portfolio: ${error.message}`);
        log('🔄 Creating new default portfolio...');
        await createDefaultPortfolio();
      }
    } else {
      log('No valid portfolio configuration found, creating default');
      await createDefaultPortfolio();
    }

    log('✅ Configuration loaded and validated successfully');
  } catch (error) {
    log(`❌ Failed to load config: ${error.message}`);
    log('Using fallback default configuration');
    await createDefaultPortfolio();
  }

  // Ensure we have a valid strategy after loading config
  if (!currentStrategy) {
    log('❌ CRITICAL: No strategy loaded after config initialization');
    throw new Error('Failed to initialize any strategy');
  }

  log(`✅ Strategy system initialized: ${currentStrategy.getName()} (${currentStrategy.constructor.name})`);
}

// Create default portfolio configuration
async function createDefaultPortfolio() {
  const defaultPortfolio = {
    strategies: [
      {
        name: 'trend-following',
        allocation: 0.4,
        config: { accountPercentToUse: 1.0 }
      },
      {
        name: 'mean-reversion',
        allocation: 0.3,
        config: { accountPercentToUse: 1.0 }
      },
      {
        name: 'breakout',
        allocation: 0.3,
        config: { accountPercentToUse: 1.0 }
      }
    ]
  };

  try {
    currentStrategy = createStrategy('portfolio-manager', defaultPortfolio, currentSymbol);

    // Initialize strategies for all active symbols
    initializeMultiSymbolStrategies('portfolio-manager', defaultPortfolio);

    log('Created default portfolio with 3 strategies');
    log(`✅ Current strategy set to: ${currentStrategy.getName()} (${currentStrategy.constructor.name})`);
    log(`🎯 Multi-symbol strategies active for: ${Array.from(symbolStrategies.keys()).join(', ')}`);

    // Save the default configuration
    saveConfig();
  } catch (error) {
    log(`❌ CRITICAL ERROR: Failed to create default portfolio: ${error.message}`);
    log('❌ Bot cannot run without PortfolioManager. Please check strategy configuration.');
    throw new Error(`PortfolioManager creation failed: ${error.message}`);
  }
}

// Save configuration and positions to file
export function saveConfig() {
  try {
    // Load existing config to preserve user settings like symbolAllocations
    let existingConfig = {};
    if (fs.existsSync(CONFIG_FILE)) {
      try {
        const configData = fs.readFileSync(CONFIG_FILE, 'utf8');
        existingConfig = JSON.parse(configData);
      } catch (error) {
        console.warn('Failed to load existing config for preservation:', error.message);
      }
    }

    // Clean portfolio config to remove duplicated symbol settings
    const cleanPortfolioConfig = { ...currentStrategy.config };
    delete cleanPortfolioConfig.symbolAllocations;
    delete cleanPortfolioConfig.activeSymbols;

    // Also clean up any existing duplicates in the loaded config
    if (existingConfig.portfolio) {
      delete existingConfig.portfolio.symbolAllocations;
      delete existingConfig.portfolio.activeSymbols;
    }

    const config = {
      maxTradingBalance: maxTradingBalance !== null ? maxTradingBalance : 0,
      activeSymbols: activeSymbols,
      // Always preserve existing symbolAllocations from file (user may have changed via sliders)
      symbolAllocations: existingConfig.symbolAllocations || symbolAllocations,
      portfolio: cleanPortfolioConfig,
      lastUpdated: new Date().toISOString()
    };

    // Update local variable to match what we're saving (keep in sync)
    if (existingConfig.symbolAllocations) {
      symbolAllocations = existingConfig.symbolAllocations;
    }

    // Save positions if using portfolio manager
    if (currentStrategy.constructor.name === 'PortfolioManager') {
      config.positions = savePositions();
      config.tradeHistory = saveTradeHistory();
      // Save unaccounted holdings (crypto not purchased by bot)
      config.unaccountedHoldings = currentStrategy.unaccountedHoldings || {};
    }

    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
  } catch (error) {
    log(`Failed to save config: ${error.message}`);
  }
}

// Save current positions to persistent storage
function savePositions() {
  try {
    if (currentStrategy.constructor.name === 'PortfolioManager') {
      const positions = {};
      const orders = {};

      // Save strategy positions (multi-currency support)
      currentStrategy.strategyPositions.forEach((strategyPositions, strategyName) => {
        const strategyData = {};
        strategyPositions.forEach((position, symbol) => {
          if (position.quantity > 0) {
            strategyData[symbol] = {
              quantity: position.quantity,
              averagePrice: position.averagePrice,
              totalCost: position.totalCost,
              unrealizedPnL: position.unrealizedPnL,
              realizedPnL: position.realizedPnL,
              symbol: position.symbol,
              lastUpdateTime: position.lastUpdateTime
            };
          }
        });

        if (Object.keys(strategyData).length > 0) {
          positions[strategyName] = strategyData;
        }
      });

      // Save active orders (stop losses, etc.)
      currentStrategy.strategyOrders.forEach((orderInfo, strategyName) => {
        if (orderInfo.activeStopLoss || orderInfo.pendingOrders.length > 0) {
          orders[strategyName] = {
            activeStopLoss: orderInfo.activeStopLoss,
            pendingOrders: orderInfo.pendingOrders,
            lastOrderTime: orderInfo.lastOrderTime
          };
        }
      });

      return { positions, orders, savedAt: Date.now() };
    }
    return null;
  } catch (error) {
    log(`Failed to save positions: ${error.message}`);
    return null;
  }
}

// Save trade history to persistent storage
function saveTradeHistory() {
  try {
    if (currentStrategy.constructor.name === 'PortfolioManager') {
      // Save last 100 trades to avoid file bloat
      const recentTrades = currentStrategy.tradeHistory.slice(-100);
      return {
        trades: recentTrades,
        savedAt: Date.now()
      };
    }
    return null;
  } catch (error) {
    log(`Failed to save trade history: ${error.message}`);
    return null;
  }
}

// Restore positions from persistent storage
function restorePositions(positionData) {
  try {
    if (currentStrategy.constructor.name !== 'PortfolioManager' || !positionData) {
      return;
    }

    const { positions, orders, savedAt } = positionData;
    const ageHours = (Date.now() - savedAt) / (1000 * 60 * 60);

    log(`🔄 Restoring positions saved ${ageHours.toFixed(1)} hours ago...`);

    // Restore strategy positions (multi-currency support)
    if (positions) {
      Object.entries(positions).forEach(([strategyName, strategyData]) => {
        const strategyPositions = new Map();

        // Handle both old single-position format and new multi-currency format
        if (strategyData.quantity !== undefined) {
          // Old format: single position
          strategyPositions.set(currentSymbol, {
            quantity: parseFloat(strategyData.quantity) || 0,
            averagePrice: parseFloat(strategyData.averagePrice) || 0,
            totalCost: parseFloat(strategyData.totalCost) || 0,
            unrealizedPnL: parseFloat(strategyData.unrealizedPnL) || 0,
            realizedPnL: parseFloat(strategyData.realizedPnL) || 0,
            symbol: currentSymbol,
            lastUpdateTime: strategyData.lastUpdateTime || Date.now()
          });
          log(`  📊 ${strategyName}: ${strategyData.quantity.toFixed(8)} ${currentSymbol.split('-')[0]} at avg $${strategyData.averagePrice.toFixed(2)} (legacy format)`);
        } else {
          // New format: multi-currency positions
          Object.entries(strategyData).forEach(([symbol, position]) => {
            strategyPositions.set(symbol, {
              quantity: parseFloat(position.quantity) || 0,
              averagePrice: parseFloat(position.averagePrice) || 0,
              totalCost: parseFloat(position.totalCost) || 0,
              unrealizedPnL: parseFloat(position.unrealizedPnL) || 0,
              realizedPnL: parseFloat(position.realizedPnL) || 0,
              symbol: position.symbol || symbol,
              lastUpdateTime: position.lastUpdateTime || Date.now()
            });
            log(`  📊 ${strategyName}: ${position.quantity.toFixed(8)} ${symbol.split('-')[0]} at avg $${position.averagePrice.toFixed(2)}`);
          });
        }

        currentStrategy.strategyPositions.set(strategyName, strategyPositions);

        // Update strategy status to reflect positions
        if (currentStrategy.strategyStatus.has(strategyName)) {
          const status = currentStrategy.strategyStatus.get(strategyName);
          const hasAnyPos = currentStrategy.hasAnyPosition(strategyName);
          currentStrategy.strategyStatus.set(strategyName, {
            ...status,
            hasPosition: hasAnyPos,
            lastDecisionReason: hasAnyPos ? `Positions restored from saved data` : 'No positions to restore'
          });
        }
      });
    }

    // Restore active orders
    if (orders) {
      Object.entries(orders).forEach(([strategyName, orderInfo]) => {
        currentStrategy.strategyOrders.set(strategyName, {
          activeStopLoss: orderInfo.activeStopLoss,
          pendingOrders: orderInfo.pendingOrders || [],
          lastOrderTime: orderInfo.lastOrderTime
        });

        if (orderInfo.activeStopLoss) {
          log(`  🛡️ ${strategyName}: Active stop loss order ${orderInfo.activeStopLoss}`);
        }
      });
    }

    log(`✅ Position restoration complete`);
  } catch (error) {
    log(`❌ Failed to restore positions: ${error.message}`);
  }
}

// Restore trade history from persistent storage
function restoreTradeHistory(tradeData) {
  try {
    if (currentStrategy.constructor.name !== 'PortfolioManager' || !tradeData) {
      return;
    }

    const { trades, savedAt } = tradeData;
    const ageHours = (Date.now() - savedAt) / (1000 * 60 * 60);

    if (trades && trades.length > 0) {
      currentStrategy.tradeHistory = [...trades];
      log(`📈 Restored ${trades.length} trades from ${ageHours.toFixed(1)} hours ago`);
    }
  } catch (error) {
    log(`❌ Failed to restore trade history: ${error.message}`);
  }
}

// Validate positions against Robinhood API on startup
async function validatePositions() {
  try {
    if (currentStrategy.constructor.name !== 'PortfolioManager') {
      return;
    }

    log('[BOT] 🔍 Validating positions against Robinhood API...');

    // Get actual holdings from Robinhood
    const holdings = await RobinhoodAPI.getHoldings();

    // Extract BTC holdings from the holdings response
    let btcHolding = null;
    if (Array.isArray(holdings)) {
      btcHolding = holdings.find(h => h.asset_code === 'BTC');
    } else if (holdings && holdings.results && Array.isArray(holdings.results)) {
      btcHolding = holdings.results.find(h => h.asset_code === 'BTC');
    }

    const actualQuantity = parseFloat(btcHolding?.total_quantity || '0');

    // Calculate total tracked quantity across all strategies
    let totalTrackedQuantity = 0;
    currentStrategy.strategyPositions.forEach(position => {
      totalTrackedQuantity += position.quantity;
    });

    const difference = Math.abs(actualQuantity - totalTrackedQuantity);
    const tolerance = 0.00000001; // 1 satoshi tolerance

    if (difference > tolerance) {
      log(`⚠️ Position mismatch detected:`);
      log(`  Robinhood: ${actualQuantity.toFixed(8)} BTC`);
      log(`  Tracked: ${totalTrackedQuantity.toFixed(8)} BTC`);
      log(`  Difference: ${difference.toFixed(8)} BTC`);

      // If we have more tracked than actual, proportionally reduce all positions
      if (totalTrackedQuantity > actualQuantity) {
        const adjustmentRatio = actualQuantity / totalTrackedQuantity;
        log(`🔧 Adjusting tracked positions by ratio: ${adjustmentRatio.toFixed(6)}`);

        currentStrategy.strategyPositions.forEach((position, strategyName) => {
          if (position.quantity > 0) {
            const adjustedQuantity = position.quantity * adjustmentRatio;
            const adjustedCost = position.totalCost * adjustmentRatio;

            currentStrategy.strategyPositions.set(strategyName, {
              ...position,
              quantity: adjustedQuantity,
              totalCost: adjustedCost
            });

            log(`  📊 ${strategyName}: ${position.quantity.toFixed(8)} → ${adjustedQuantity.toFixed(8)} BTC`);
          }
        });
      }
      // If actual is more than tracked, log warning but don't auto-adjust
      else {
        log(`⚠️ Robinhood shows more BTC than tracked. Manual review recommended.`);
      }

      // Save corrected positions
      saveConfig();
    } else {
      log(`✅ Position validation passed: ${actualQuantity.toFixed(8)} BTC`);
    }

  } catch (error) {
    log(`❌ Failed to validate positions: ${error.message}`);
  }
}

// Helper function to get strategy name from class
function getStrategyName(strategy) {
  if (strategy.constructor.name === 'PortfolioManager') {
    return 'portfolio-manager';
  }
  
  // Convert class name to kebab-case
  return strategy.constructor.name
    .replace('Strategy', '')
    .replace(/([A-Z])/g, (match, letter, index) => {
      return index === 0 ? letter.toLowerCase() : '-' + letter.toLowerCase();
    });
}

// Process exit handlers to save data
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, saving data and shutting down...');
  try {
    candleService.forceSave();
    saveConfig();
    console.log('💾 Data saved successfully');
  } catch (error) {
    console.error('❌ Failed to save data on exit:', error);
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, saving data and shutting down...');
  try {
    candleService.forceSave();
    saveConfig();
    console.log('💾 Data saved successfully');
  } catch (error) {
    console.error('❌ Failed to save data on exit:', error);
  }
  process.exit(0);
});

// Export functions for other modules to access bot state
// Note: getBotStats() already exists as an async function at line 96

// Load config on module initialization
loadConfig().catch(error => {
  console.error('Failed to load config on startup:', error.message);
});
