import { createStrategy } from './index.js';
import { getCandleAggregatorService } from '../services/CandleAggregatorService.js';
import { getTradingEventEmitter } from '../services/TradingEventEmitter.js';
import Big from 'big.js';
import fs from 'fs';
import path from 'path';

export class PortfolioManager {
  constructor(config = {}, symbol = 'BTC-USD') {
    this.primarySymbol = symbol; // Primary symbol for legacy compatibility
    this.config = {
      strategies: [], // Array of {name, config, allocation}
      rebalanceFrequency: 24 * 60 * 60 * 1000, // 24 hours in ms
      performanceWindow: 7 * 24 * 60 * 60 * 1000, // 7 days for performance calc
      minAllocation: 0.05, // Minimum 5% allocation per strategy
      maxAllocation: 0.6, // Maximum 60% allocation per strategy
      rebalanceThreshold: 0.1, // Rebalance when performance diff > 10%
      ...config
    };

    // Remove symbolAllocations and activeSymbols from config if they exist
    // These should only be stored at root level, not in portfolio config
    delete this.config.symbolAllocations;
    delete this.config.activeSymbols;

    // Load symbol allocations from config file (stored separately from portfolio config)
    this.symbolAllocations = { 'BTC-USD': 1.0 }; // Default
    this.activeSymbols = ['BTC-USD']; // Default
    this.loadSymbolAllocations();

    // RobinhoodAPI will be initialized lazily when needed
    this.robinhoodAPI = null;
    
    this.strategyInstances = new Map();
    this.strategyPerformance = new Map();
    this.strategyAllocations = new Map();
    this.strategyStatus = new Map(); // Track current status of each strategy
    this.strategyPositions = new Map(); // strategy -> Map<symbol, position> for multi-currency support
    this.strategyOrders = new Map(); // Track active orders per strategy
    this.lastRebalance = Date.now();
    this.tradeHistory = [];
    this.lastPrice = 0;
    this.cachedActualBalance = null; // Cache for dynamic balance calculation
    
    // Initialize strategies asynchronously
    this.initializeStrategies().then(() => {
      // Check if positions need recovery/cleanup
      this.checkAndRecoverPositions();

      // Perform initial strategy evaluation to set status immediately
      this.performInitialEvaluation();
    }).catch(error => {
      console.error('[PORTFOLIO] ❌ Failed to initialize strategies:', error);
    });
  }

  loadSymbolAllocations() {
    try {
      const configPath = 'data/bot-data.json';
      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        const config = JSON.parse(configData);

        if (config.symbolAllocations) {
          this.symbolAllocations = config.symbolAllocations;
          console.log(`[PORTFOLIO] 📊 Loaded symbol allocations:`, this.symbolAllocations);
        }

        if (config.activeSymbols) {
          this.activeSymbols = config.activeSymbols;
          console.log(`[PORTFOLIO] 📊 Loaded active symbols:`, this.activeSymbols);
        }
      }
    } catch (error) {
      console.log(`[PORTFOLIO] ⚠️ Could not load symbol allocations: ${error.message}`);
      // Use defaults (already set in constructor)
    }
  }

  async initializeStrategies() {
    console.log('🔧 Initializing portfolio strategies...');
    const candleService = getCandleAggregatorService();

    // Import the multi-symbol service for dynamic symbol evaluation
    const { getMultiSymbolCandleService } = await import('../services/MultiSymbolCandleService.js');
    const multiCandleService = getMultiSymbolCandleService();

    this.config.strategies.forEach(strategyConfig => {
      const strategy = createStrategy(strategyConfig.name, strategyConfig.config);

      // Set strategy name for proper signaling
      strategy.strategyName = strategyConfig.name;

      // Inject candle services into strategy
      if (candleService && multiCandleService) {
        strategy.candleService = candleService; // Default BTC-USD service for backward compatibility
        strategy.multiCandleService = multiCandleService; // Multi-symbol service for dynamic symbol evaluation
        console.log(`🔗 Injected candle services into ${strategyConfig.name}`);
      } else {
        console.warn(`⚠️ No candle service available for ${strategyConfig.name}`);
      }

      // Initialize price history from existing candle data
      console.log(`🔄 Initializing price history for ${strategyConfig.name}...`);
      strategy.initializePriceHistory();

      this.strategyInstances.set(strategyConfig.name, strategy);
      this.strategyAllocations.set(strategyConfig.name, strategyConfig.allocation || 1 / this.config.strategies.length);
      this.strategyPerformance.set(strategyConfig.name, {
        trades: [], // Completed buy-sell pairs
        individualTrades: [], // All individual trades (buy and sell)
        totalReturn: 0,
        winRate: 0,
        sharpeRatio: 0,
        lastUpdate: Date.now()
      });
      this.strategyStatus.set(strategyConfig.name, {
        lastDecision: null,
        lastDecisionTime: null,
        lastDecisionReason: 'Strategy initialized, waiting for first price update',
        nextSignal: 'Waiting for market data',
        hasPosition: false,
        lastTradeTime: null,
        lastTradeType: null,
        lastTradePrice: null
      });

      // Initialize position tracking for each strategy (multi-currency support)
      this.strategyPositions.set(strategyConfig.name, new Map());

      // Initialize order tracking for each strategy
      this.strategyOrders.set(strategyConfig.name, {
        activeStopLoss: null,
        pendingOrders: [],
        lastOrderTime: null
      });
    });

    // Set up event-driven trading
    this.tradingEvents = getTradingEventEmitter();
    this.setupEventListeners();

    console.log(`✅ PortfolioManager initialized with ${this.config.strategies.length} strategies (event-driven)`);

    // Ensure allocations are properly normalized after initialization
    this.verifyAndFixAllocations();

    // Fix any existing positions with missing price data on startup
    setTimeout(() => {
      this.fixPositionsWithMissingPriceData().catch(error => {
        console.error('[PORTFOLIO] ❌ Failed to fix positions on startup:', error.message);
      });
    }, 3000); // Wait 3 seconds for system to stabilize
  }

  // Initialize RobinhoodAPI for position reconciliation
  async initializeRobinhoodAPI() {
    try {
      // Import RobinhoodAPI dynamically to avoid circular dependencies
      const { RobinhoodAPI } = await import('../robinhoodClient.js');
      this.robinhoodAPI = RobinhoodAPI;
      console.log('[PORTFOLIO] ✅ RobinhoodAPI initialized for position reconciliation');
    } catch (error) {
      console.error('[PORTFOLIO] ❌ Failed to initialize RobinhoodAPI:', error.message);
      this.robinhoodAPI = null;
    }
  }

  // Set up event listeners for trading signals
  setupEventListeners() {
    // Listen for strategy trade ready signals
    this.tradingEvents.on('tradeReady', async (event) => {
      await this.handleTradeReadySignal(event);
    });

    // Listen for strategy not ready signals
    this.tradingEvents.on('tradeNotReady', (event) => {
      this.handleTradeNotReadySignal(event);
    });

    // Listen for new multi-symbol trade signals
    this.tradingEvents.on('tradeSignal', async (signal) => {
      await this.handleTradeSignal(signal);
    });

    // Listen for price updates to update strategy statuses
    this.tradingEvents.on('priceUpdate', async (priceData) => {
      // Process price updates for all active symbols
      if (this.activeSymbols.includes(priceData.symbol)) {
        await this.updateStrategyStatusesOnPriceUpdate(priceData);
      }
    });
  }

  // Initialize strategy statuses for event-driven system
  performInitialEvaluation() {
    console.log('🔄 Initializing strategy statuses for event-driven system...');

    // Don't use mock price - wait for real price updates to avoid incorrect status displays
    // Set initial status for all strategies without mock evaluation
    this.strategyInstances.forEach((_, name) => {
      const allocation = this.strategyAllocations.get(name);
      const strategyBalance = this.getActualMaxTradingBalanceSync() * allocation;

      if (strategyBalance < 5) {
        // Mark underfunded strategies
        this.strategyStatus.set(name, {
          lastDecision: false,
          lastDecisionTime: Date.now(),
          lastDecisionReason: `Insufficient allocation: $${strategyBalance.toFixed(2)} (minimum $5 required)`,
          nextSignal: `Need $${(5 - strategyBalance).toFixed(2)} more. Increase max trading balance or reduce number of strategies.`,
          hasPosition: false,
          lastTradeTime: null,
          lastTradeType: null,
          lastTradePrice: null,
          isUnderfunded: true,
          requiredIncrease: 5 - strategyBalance
        });
      } else {
        // Set initial status without mock price evaluation
        // Real evaluation will happen when actual price updates arrive via event-driven system
        this.strategyStatus.set(name, {
          lastDecision: false,
          lastDecisionTime: Date.now(),
          lastDecisionReason: 'Waiting for price updates to begin evaluation',
          nextSignal: 'Analyzing market conditions...',
          hasPosition: false,
          lastTradeTime: null,
          lastTradeType: null,
          lastTradePrice: null,
          isUnderfunded: false
        });
      }
    });

    console.log('✅ Event-driven strategy initialization complete - waiting for real price data');

    // Display unaccounted holdings information if any exist
    this.displayUnaccountedHoldings();

    // Clean up any corrupted performance data
    this.cleanupCorruptedPerformanceData();

    // Check and sync positions on startup
    setTimeout(() => {
      this.checkAndUpdateFilledOrders().catch(error => {
        console.error(`[PORTFOLIO] ❌ Startup position check failed:`, error.message);
      });
    }, 2000); // Wait 2 seconds for system to stabilize

  // Start automatic order reconciliation
  this.startOrderReconciliation();
  }

  // Handle strategy signaling it's ready to trade
  async handleTradeReadySignal(event) {
    const { strategyName, signal, currentPrice } = event;

    console.log(`[PORTFOLIO] 🚀 ${strategyName} ready to trade: ${signal.reason}`);
    console.log(`[PORTFOLIO] 🔍 DEBUG: Event details:`, { strategyName, signal, currentPrice });

    try {
      // Check if strategy is properly funded and can trade
      const allocation = this.strategyAllocations.get(strategyName) || 0;
      const availableBalance = this.getActualMaxTradingBalanceSync();
      const strategyBalance = availableBalance * allocation;

      console.log(`[PORTFOLIO] 🔍 DEBUG: Funding check for ${strategyName}:`);
      console.log(`  - Allocation: ${(allocation * 100).toFixed(1)}%`);
      console.log(`  - Max Trading Balance: $${availableBalance.toFixed(2)}`);
      console.log(`  - Strategy balance: $${strategyBalance.toFixed(2)}`);
      console.log(`  - Minimum required: $5.00`);

      if (strategyBalance < 5) { // Minimum $5 to trade
        console.log(`[PORTFOLIO] ❌ ${strategyName} insufficient funds: $${strategyBalance.toFixed(2)} < $5`);
        return;
      }

      // Check if strategy already has a position
      const position = this.getStrategyPosition(strategyName);
      console.log(`[PORTFOLIO] 🔍 DEBUG: Position check for ${strategyName}:`);
      console.log(`  - Current position: ${position.quantity.toFixed(8)} BTC`);
      console.log(`  - Has position: ${position.quantity > 0}`);

      if (position.quantity > 0) {
        console.log(`[PORTFOLIO] ❌ ${strategyName} already has position: ${position.quantity} BTC`);
        return;
      }

      // Execute the actual trade
      console.log(`[PORTFOLIO] ✅ Executing trade for ${strategyName} with $${strategyBalance.toFixed(2)}`);
      console.log(`[PORTFOLIO] 📊 Signal: ${signal.reason} (confidence: ${signal.confidence || 'unknown'})`);

      // Mark trading system as processing to prevent conflicts
      this.tradingEvents.setProcessingTrade(true);

      try {
        console.log(`[PORTFOLIO] 🔍 DEBUG: About to call executeBuyOrder with:`);
        console.log(`  - strategyName: ${strategyName}`);
        console.log(`  - strategyBalance: $${strategyBalance.toFixed(2)}`);
        console.log(`  - currentPrice: $${currentPrice.toFixed(2)}`);

        // Execute the buy order
        console.log(`[PORTFOLIO] 🚀 Calling executeBuyOrder...`);
        await this.executeBuyOrder(strategyName, strategyBalance, currentPrice, signal);
        console.log(`[PORTFOLIO] ✅ executeBuyOrder completed successfully`);

        // Clear all strategy signals since a trade was executed
        this.tradingEvents.clearAllSignals('Trade executed successfully');

      } catch (error) {
        console.error(`[PORTFOLIO] ❌ Trade execution failed for ${strategyName}:`, error);
      } finally {
        // Always release the trading lock
        console.log(`[PORTFOLIO] 🔓 Releasing trading lock after ${strategyName} attempt`);
        this.tradingEvents.setProcessingTrade(false);
      }

    } catch (error) {
      console.error(`[PORTFOLIO] ❌ Error handling trade signal from ${strategyName}:`, error);
    }
  }

  // Handle trade signals from strategies (new multi-symbol approach)
  async handleTradeSignal(signal) {
    const { strategy: strategyName, action, price: currentPrice, reason, confidence, symbol } = signal;
    const targetSymbol = symbol || this.primarySymbol;
    const assetCode = targetSymbol.split('-')[0];

    console.log(`[PORTFOLIO] 🎯 Trade signal received: ${strategyName} wants to ${action} ${targetSymbol} at $${currentPrice.toFixed(2)}`);
    console.log(`[PORTFOLIO] 🎯 Reason: ${reason}`);

    if (action !== 'buy') {
      console.log(`[PORTFOLIO] ❌ Only buy signals are currently supported`);
      return;
    }

    try {
      // Check if strategy is properly funded and can trade
      const allocation = this.strategyAllocations.get(strategyName) || 0;
      const availableBalance = this.getActualMaxTradingBalanceSync();
      const strategyBalance = availableBalance * allocation;

      console.log(`[PORTFOLIO] 🔍 DEBUG: Funding check for ${strategyName} on ${targetSymbol}:`);
      console.log(`  - Allocation: ${(allocation * 100).toFixed(1)}%`);
      console.log(`  - Max Trading Balance: $${availableBalance.toFixed(2)}`);
      console.log(`  - Strategy balance: $${strategyBalance.toFixed(2)}`);
      console.log(`  - Minimum required: $5.00`);

      if (strategyBalance < 5) { // Minimum $5 to trade
        console.log(`[PORTFOLIO] ❌ ${strategyName} insufficient funds: $${strategyBalance.toFixed(2)} < $5`);
        console.log(`[PORTFOLIO] 💡 Increase max trading balance to at least $${(5 / allocation).toFixed(2)} to enable trading for this strategy`);
        return;
      }

      // Check if strategy already has a position in this specific symbol
      const position = this.getStrategyPosition(strategyName, targetSymbol);
      console.log(`[PORTFOLIO] 🔍 DEBUG: Position check for ${strategyName} on ${targetSymbol}:`);
      console.log(`  - Current position: ${position.quantity.toFixed(8)} ${assetCode}`);
      console.log(`  - Has position: ${position.quantity > 0}`);

      if (position.quantity > 0) {
        console.log(`[PORTFOLIO] ❌ ${strategyName} already has position: ${position.quantity} ${assetCode}`);
        return;
      }

      // Execute the actual trade
      console.log(`[PORTFOLIO] ✅ Executing trade for ${strategyName} with $${strategyBalance.toFixed(2)}`);
      console.log(`[PORTFOLIO] 📊 Signal: ${reason} (confidence: ${confidence || 'unknown'})`);

      await this.executeBuyOrder(strategyName, strategyBalance, currentPrice, signal, targetSymbol);

    } catch (error) {
      console.error(`[PORTFOLIO] ❌ Error handling trade signal for ${strategyName}:`, error.message);
    }
  }

  // Execute a buy order for a strategy
  async executeBuyOrder(strategyName, strategyBalance, currentPrice, signal, symbol = null) {
    const targetSymbol = symbol || this.primarySymbol;
    console.log(`[PORTFOLIO] 🔍 DEBUG: executeBuyOrder started for ${strategyName} on ${targetSymbol}`);

    try {
      console.log(`[PORTFOLIO] 🔍 DEBUG: Importing dependencies...`);
      // Import RobinhoodAPI dynamically to avoid circular dependency
      const { RobinhoodAPI } = await import('../robinhoodClient.js');
      const { v4: uuidv4 } = await import('uuid');
      console.log(`[PORTFOLIO] ✅ Dependencies imported successfully`);

      console.log(`[PORTFOLIO] 🔍 DEBUG: Calculating position size...`);
      // Calculate position size based on strategy balance
      const positionSize = this.calculatePositionSize(strategyBalance, currentPrice);
      const quantity = positionSize.quantity.toFixed(8);
      const assetCode = symbol.split('-')[0];
      console.log(`[PORTFOLIO] ✅ Position size calculated: ${quantity} ${assetCode}`);

      console.log(`[PORTFOLIO] 💰 Buying ${quantity} ${assetCode} for ${strategyName} at $${currentPrice.toFixed(2)}`);
      console.log(`[PORTFOLIO] 📊 Position size: ${positionSize.reason}`);

      // Generate unique order ID
      const orderId = uuidv4();
      console.log(`[PORTFOLIO] 🔍 DEBUG: Generated order ID: ${orderId}`);

      // Track pending order
      console.log(`[PORTFOLIO] 🔍 DEBUG: Adding pending order...`);
      this.addPendingOrder(strategyName, orderId, 'market', 'buy', quantity);
      console.log(`[PORTFOLIO] ✅ Pending order added`);

      // Place the order with Robinhood
      console.log(`[PORTFOLIO] 🔍 DEBUG: About to place order with Robinhood...`);
      console.log(`[PORTFOLIO] 🔍 DEBUG: Order details:`, {
        symbol: targetSymbol,
        clientOrderId: orderId,
        side: 'buy',
        type: 'market',
        config: { asset_quantity: quantity }
      });

      // Check available buying power before placing order
      const orderValue = currentPrice * quantity;
      console.log(`[PORTFOLIO] 🔍 Checking buying power: Order value $${orderValue.toFixed(2)}`);

      try {
        // Use Max Trading Balance as the buying power limit, not actual account balance
        const maxTradingBalance = this.getActualMaxTradingBalanceSync();

        // Check if trading is disabled (Max Trading Balance = $0)
        if (maxTradingBalance === 0) {
          console.log(`[PORTFOLIO] 🚫 Trading disabled: Max Trading Balance is $0.00`);
          console.log(`[PORTFOLIO] 💡 Set Max Trading Balance > $0 to enable new trades`);
          this.removePendingOrder(strategyName, orderId);
          return;
        }

        // Calculate how much of the Max Trading Balance is already used by existing positions
        const totalPositionValue = await this.getTotalPortfolioValue();
        const availableTradingBalance = Math.max(0, maxTradingBalance - totalPositionValue);

        console.log(`[PORTFOLIO] 💰 Max Trading Balance: $${maxTradingBalance.toFixed(2)}`);
        console.log(`[PORTFOLIO] 📊 Current position value: $${totalPositionValue.toFixed(2)}`);
        console.log(`[PORTFOLIO] 💰 Available trading balance: $${availableTradingBalance.toFixed(2)}, Required: $${orderValue.toFixed(2)}`);

        if (availableTradingBalance < orderValue) {
          console.log(`[PORTFOLIO] ❌ Insufficient trading balance for ${strategyName}: $${availableTradingBalance.toFixed(2)} < $${orderValue.toFixed(2)}`);
          console.log(`[PORTFOLIO] 💡 Consider increasing Max Trading Balance or selling positions to free up funds`);

          // Remove pending order since we won't place it
          this.removePendingOrder(strategyName, orderId);
          return;
        }

        console.log(`[PORTFOLIO] ✅ Sufficient trading balance available within Max Trading Balance limit`);

        // Also check actual Robinhood buying power as a secondary check
        try {
          const { RobinhoodAPI } = await import('../robinhoodClient.js');
          const account = await RobinhoodAPI.getAccount();

          // Try multiple fields for buying power
          const buyingPowerFields = [
            account?.crypto_buying_power,
            account?.buying_power,
            account?.cash?.total_available,
            account?.cash_available_for_withdrawal,
            account?.cash_balances?.buying_power
          ];

          let actualBuyingPower = 0;
          for (const field of buyingPowerFields) {
            if (field && parseFloat(field) > 0) {
              actualBuyingPower = parseFloat(field);
              break;
            }
          }

          console.log(`[PORTFOLIO] 🔍 DEBUG: Account buying power fields:`, {
            crypto_buying_power: account?.crypto_buying_power,
            buying_power: account?.buying_power,
            cash_total_available: account?.cash?.total_available,
            cash_available_for_withdrawal: account?.cash_available_for_withdrawal,
            selected_value: actualBuyingPower
          });

          if (actualBuyingPower < orderValue) {
            console.log(`[PORTFOLIO] ⚠️ Warning: Robinhood account has insufficient buying power: $${actualBuyingPower.toFixed(2)} < $${orderValue.toFixed(2)}`);
            console.log(`[PORTFOLIO] 💡 Order may fail due to insufficient account funds, but proceeding based on Max Trading Balance`);
          } else {
            console.log(`[PORTFOLIO] ✅ Robinhood account has sufficient buying power: $${actualBuyingPower.toFixed(2)} >= $${orderValue.toFixed(2)}`);
          }
        } catch (accountError) {
          console.log(`[PORTFOLIO] ⚠️ Could not verify Robinhood account buying power: ${accountError.message}`);
        }

      } catch (error) {
        console.log(`[PORTFOLIO] ⚠️ Could not verify trading balance: ${error.message}`);
        console.log(`[PORTFOLIO] 🔄 Proceeding with order placement (will let Robinhood validate)`);
      }

      // Format quantity according to symbol precision to avoid Robinhood validation errors
      const symbolConfig = await import('../config/SymbolConfig.js').then(m => m.getSymbolConfig());
      await symbolConfig.initialize();

      // Debug: Check symbol metadata
      const metadata = symbolConfig.symbolMetadata?.get(targetSymbol);
      console.log(`[PORTFOLIO] 🔍 DEBUG: Symbol metadata for ${targetSymbol}:`, metadata);

      let formattedQuantity = symbolConfig.formatQuantity(targetSymbol, quantity);

      // Robinhood precision requirements based on actual API error messages
      const robinhoodPrecision = {
        'BTC-USD': 8,  // 0.********
        'ETH-USD': 6,  // 0.000001
        'DOGE-USD': 2, // 0.01 (based on error: "round it to nearest 0.010000000000000000")
        'LTC-USD': 6,  // 0.000001 (based on error: "round it to nearest 0.********0000000000")
        'BCH-USD': 6,  // 0.000001 (conservative estimate)
        'SOL-USD': 3,  // 0.001 (based on error: "round it to nearest 0.001000000000000000")
        'XRP-USD': 2   // 0.01 (conservative estimate)
      };

      const requiredDecimals = robinhoodPrecision[targetSymbol] || 6;

      // Round to the required precision, removing any trailing precision
      const roundedQuantity = Math.round(parseFloat(quantity) * Math.pow(10, requiredDecimals)) / Math.pow(10, requiredDecimals);
      formattedQuantity = roundedQuantity.toFixed(requiredDecimals);

      console.log(`[PORTFOLIO] 🔍 DEBUG: Formatted quantity: ${quantity} → ${formattedQuantity} for ${targetSymbol} (${requiredDecimals} decimals)`);

      const orderResult = await RobinhoodAPI.placeOrder({
        symbol: targetSymbol,
        clientOrderId: orderId,
        side: 'buy',
        type: 'market',
        config: { asset_quantity: formattedQuantity }
      });

      console.log(`[PORTFOLIO] 🔍 DEBUG: Order placement result:`, orderResult);

      if (orderResult) {
        console.log(`[PORTFOLIO] ✅ Buy order placed successfully for ${strategyName}`);
        const assetCode = targetSymbol.split('-')[0];
        console.log(`[PORTFOLIO] 📋 Order ID: ${orderId}, Quantity: ${parseFloat(formattedQuantity).toFixed(8)} ${assetCode}`);

        // Record trade immediately for position tracking and UI updates
        // Market orders are typically filled immediately, so we can safely record the trade
        console.log(`[PORTFOLIO] 📝 Recording trade immediately for position tracking`);
        this.recordTrade('buy', currentPrice, formattedQuantity, strategyName, targetSymbol);

        // Force an immediate balance update to reflect the trade
        console.log(`[PORTFOLIO] 🔄 Forcing balance update after trade`);
        this.updateCachedActualBalance().catch(error => {
          console.warn('[PORTFOLIO] ⚠️  Failed to update cached balance after trade:', error.message);
        });

        // Remove from pending orders (order was successfully placed)
        this.removePendingOrder(strategyName, orderId);

        return orderResult;
      } else {
        console.error(`[PORTFOLIO] ❌ Failed to place buy order for ${strategyName}`);
        console.log(`[PORTFOLIO] 💡 This is likely due to insufficient buying power or all funds being allocated to existing positions`);
        this.removePendingOrder(strategyName, orderId);
        throw new Error('Order placement failed - likely insufficient buying power');
      }

    } catch (error) {
      console.error(`[PORTFOLIO] ❌ Error executing buy order for ${strategyName}:`, error);
      console.error(`[PORTFOLIO] 🔍 DEBUG: Error details:`, {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      throw error;
    }
  }

  // Calculate position size for a strategy
  calculatePositionSize(strategyBalance, currentPrice) {
    // Use 95% of available balance to leave room for fees
    const usableBalance = strategyBalance * 0.95;
    const quantity = usableBalance / currentPrice;

    // Minimum position size check
    if (quantity < 0.00001) {
      throw new Error(`Position size too small: ${quantity} BTC (minimum 0.00001)`);
    }

    return {
      quantity: quantity,
      reason: `Using $${usableBalance.toFixed(2)} of $${strategyBalance.toFixed(2)} available (95% utilization)`
    };
  }

  // Get actual max trading balance from bot configuration
  getActualMaxTradingBalance() {
    try {
      // Import bot module dynamically to avoid circular dependency
      const botModule = import('../bot.js');
      return botModule.then(bot => {
        const maxBalance = bot.getMaxTradingBalance();
        // If no max balance is set, disable trading
        return maxBalance || 0; // No trading if not configured
      }).catch(() => {
        console.warn('[PORTFOLIO] ⚠️  Could not get max trading balance from bot, trading disabled');
        return 0; // No trading if can't access bot config
      });
    } catch (error) {
      console.warn('[PORTFOLIO] ⚠️  Error getting max trading balance:', error.message);
      return 0; // No trading if error occurs
    }
  }

  // Get actual max trading balance synchronously (for immediate use)
  getActualMaxTradingBalanceSync() {
    try {
      // Try to read from config file using imported modules
      const configPath = path.join(process.cwd(), 'data', 'bot-data.json');

      if (fs.existsSync(configPath)) {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        const maxBalance = config.maxTradingBalance;

        // Respect $0 setting - it means no trading allowed
        // Also treat null as 0 for safety
        if (maxBalance !== undefined && maxBalance !== null) {
          console.log(`[PORTFOLIO] 📊 Using configured max trading balance: $${maxBalance.toFixed(2)}`);
          return maxBalance; // Return even if $0 - this disables trading
        } else if (maxBalance === null) {
          console.log(`[PORTFOLIO] 📊 Max trading balance is null, defaulting to $0 (trading disabled)`);
          return 0; // Treat null as 0 for safety
        }
      }

      // Fallback: Use actual account balance when no limit is set
      console.log('[PORTFOLIO] 💰 No max trading balance limit set, using dynamic calculation');

      // Use the cached balance if available, otherwise use a reasonable default
      if (this.cachedActualBalance && this.cachedActualBalance > 0) {
        console.log(`[PORTFOLIO] 📊 Using cached actual balance: $${this.cachedActualBalance.toFixed(2)}`);
        return this.cachedActualBalance;
      }

      // Fallback to a reasonable default (will be updated when balance is cached)
      console.log('[PORTFOLIO] 📊 Using fallback balance until actual balance is cached');
      return 80;

    } catch (error) {
      console.warn('[PORTFOLIO] ⚠️  Error getting max trading balance sync:', error.message);
      return 80; // Reasonable fallback
    }
  }

  // Check if a strategy has an open position
  hasPosition(strategyName) {
    const position = this.strategyPositions.get(strategyName);
    return position ? position.quantity > 0 : false;
  }

  // Check order status and update positions for filled orders
  async checkAndUpdateFilledOrders() {
    try {
      // Import RobinhoodAPI dynamically
      const { RobinhoodAPI } = await import('../robinhoodClient.js');

      // Get current holdings from Robinhood
      const holdings = await RobinhoodAPI.getHoldings();

      // Debug: Log holdings structure
      console.log(`[PORTFOLIO] 🔍 Holdings response type: ${typeof holdings}, isArray: ${Array.isArray(holdings)}`);
      if (holdings && holdings.results) {
        console.log(`[PORTFOLIO] 🔍 Holdings.results length: ${holdings.results.length}`);
      }

      // Get symbol config for asset code lookups
      const symbolConfig = await import('../config/SymbolConfig.js').then(m => m.getSymbolConfig());

      // Check each active symbol
      for (const symbol of this.activeSymbols) {
        await this.checkSymbolPositions(symbol, holdings, symbolConfig, RobinhoodAPI);
      }

    } catch (error) {
      console.error(`[PORTFOLIO] ❌ Error checking filled orders:`, error.message);
    }
  }

  async checkSymbolPositions(symbol, holdings, symbolConfig, RobinhoodAPI) {
    try {
      const assetCode = symbolConfig.getAssetCode(symbol);

      // Handle different response formats
      let currentAssetHoldings = null;
      if (Array.isArray(holdings)) {
        currentAssetHoldings = holdings.find(h => h.asset_code === assetCode || h.currency_code === assetCode);
      } else if (holdings && holdings.results && Array.isArray(holdings.results)) {
        currentAssetHoldings = holdings.results.find(h => h.asset_code === assetCode || h.currency_code === assetCode);
      }

      const actualAssetQuantity = currentAssetHoldings ? parseFloat(currentAssetHoldings.total_quantity || currentAssetHoldings.quantity || '0') : 0;

      // Debug: Log asset holdings details
      if (currentAssetHoldings) {
        console.log(`[PORTFOLIO] 🔍 ${assetCode} holding found:`, {
          asset_code: currentAssetHoldings.asset_code,
          currency_code: currentAssetHoldings.currency_code,
          total_quantity: currentAssetHoldings.total_quantity,
          quantity: currentAssetHoldings.quantity
        });
      } else {
        console.log(`[PORTFOLIO] 🔍 No ${assetCode} holdings found in response`);
      }

      // Calculate total tracked positions for this symbol
      let totalTrackedQuantity = 0;
      this.strategyPositions.forEach(strategyPositions => {
        if (strategyPositions.has(symbol)) {
          const position = strategyPositions.get(symbol);
          totalTrackedQuantity += parseFloat(position.quantity) || 0;
        }
      });

      console.log(`[PORTFOLIO] 🔍 Position check: Actual ${actualAssetQuantity.toFixed(8)} ${assetCode}, Tracked ${totalTrackedQuantity.toFixed(8)} ${assetCode}`);

      // Check for open orders that might be locking up assets
      console.log(`[PORTFOLIO] 🔍 Checking for open orders that might lock ${assetCode}...`);
      const openOrders = await RobinhoodAPI.getOpenOrders(symbol);

      if (openOrders && openOrders.results && openOrders.results.length > 0) {
        console.log(`[PORTFOLIO] ⚠️  Found ${openOrders.results.length} open ${assetCode} orders:`);
        openOrders.results.forEach(order => {
          // Debug: Log the full order structure to see available fields
          console.log(`[PORTFOLIO] 🔍 DEBUG: Order structure:`, JSON.stringify(order, null, 2));

          // Extract quantity based on actual Robinhood API response structure
          let quantity = 'unknown';

          // First, try to get the original order quantity from the appropriate config
          const configs = [
            order.market_order_config,
            order.limit_order_config,
            order.stop_loss_order_config,
            order.stop_limit_order_config
          ];

          for (const config of configs) {
            if (config && config.asset_quantity) {
              quantity = config.asset_quantity;
              break;
            }
            if (config && config.quote_amount) {
              quantity = config.quote_amount + ' USD';
              break;
            }
          }

          // If still unknown, try top-level fields
          if (quantity === 'unknown') {
            quantity = order.filled_asset_quantity ||
                      order.asset_quantity ||
                      order.quantity ||
                      'unknown';
          }

          // Add execution info if available
          let executionInfo = '';
          if (order.filled_asset_quantity && order.filled_asset_quantity > 0) {
            executionInfo = ` (filled: ${order.filled_asset_quantity})`;
          }
          console.log(`  📋 Order ${order.id}: ${order.side} ${order.type} ${quantity} ${assetCode} (${order.state})${executionInfo}`);
        });

        // Calculate assets locked in open orders
        let lockedAssets = 0;
        openOrders.results.forEach(order => {
          if (order.side === 'sell' && order.asset_quantity) {
            lockedAssets += parseFloat(order.asset_quantity);
          }
        });

        if (lockedAssets > 0) {
          console.log(`[PORTFOLIO] 🔒 ${lockedAssets.toFixed(8)} ${assetCode} locked in open sell orders`);
          console.log(`[PORTFOLIO] 💡 Available for new orders: ${(actualAssetQuantity - lockedAssets).toFixed(8)} ${assetCode}`);
        }
      } else {
        console.log(`[PORTFOLIO] ✅ No open ${assetCode} orders found`);
      }

      // If there's a significant difference, sync positions with actual holdings
      const difference = Math.abs(actualAssetQuantity - totalTrackedQuantity);
      if (difference > 0.********) { // 1 satoshi tolerance
        console.log(`[PORTFOLIO] ⚠️  Position mismatch detected: ${difference.toFixed(8)} ${assetCode} difference`);

        // Only handle significant mismatches here - let main reconciliation handle dust
        if (actualAssetQuantity > totalTrackedQuantity && difference > 0.0001) {
          // Only sync when Robinhood has significantly more than we track
          console.log(`[PORTFOLIO] 🔄 Significant position mismatch - deferring to main reconciliation system`);
          // Don't auto-sync here - let the main reconciliation system handle it properly
        } else if (actualAssetQuantity === 0 && totalTrackedQuantity < 0.0001) {
          // Very small dust amounts - let main reconciliation handle this
          console.log(`[PORTFOLIO] 📝 Small position mismatch detected - will be handled by main reconciliation`);
        } else {
          console.log(`[PORTFOLIO] 📝 Position mismatch logged - main reconciliation will handle appropriately`);
        }
      }

    } catch (error) {
      console.error(`[PORTFOLIO] ❌ Error checking ${symbol} positions:`, error.message);
    }
  }

  // Clean up dust positions (very small amounts that are essentially zero)
  cleanupDustPositions(symbol) {
    const symbolConfig = import('../config/SymbolConfig.js').then(m => m.getSymbolConfig());
    const assetCode = symbolConfig.getAssetCode ? symbolConfig.getAssetCode(symbol) : symbol.split('-')[0];

    let cleanedCount = 0;

    this.strategyPositions.forEach((strategyPositions, strategyName) => {
      if (strategyPositions.has(symbol)) {
        const position = strategyPositions.get(symbol);

        // Only clean positions for the specified symbol
        if (position.quantity > 0 && position.quantity < 0.0001) {
          console.log(`[PORTFOLIO] 🧹 Cleaning dust from ${strategyName}: ${position.quantity.toFixed(8)} ${assetCode}`);

          strategyPositions.set(symbol, {
            ...position,
            quantity: 0,
            totalCost: 0,
            unrealizedPnL: 0,
            lastUpdateTime: Date.now()
          });

          cleanedCount++;
        }
      }
    });

    if (cleanedCount > 0) {
      console.log(`[PORTFOLIO] ✅ Cleaned ${cleanedCount} dust positions for ${assetCode}`);
      this.savePositionsToFile();
    }
  }

  // Update cached actual balance for dynamic max trading balance calculation
  async updateCachedActualBalance(actualAssetQuantity = null) {
    try {
      // Import RobinhoodAPI dynamically
      const { RobinhoodAPI } = await import('../robinhoodClient.js');

      // Get account info to get cash balance
      const account = await RobinhoodAPI.getAccount();
      const cashBalance = parseFloat(account?.buying_power || account?.cash?.total_available || '0');

      // Get crypto holdings for all active symbols
      const holdings = await RobinhoodAPI.getHoldings();
      const symbolConfig = await import('../config/SymbolConfig.js').then(m => m.getSymbolConfig());

      // Get current prices for all active symbols
      if (!this.activeSymbols || this.activeSymbols.length === 0) {
        console.warn('[PORTFOLIO] ⚠️  No active symbols available for price fetching, using default');
        this.activeSymbols = ['BTC-USD']; // Fallback to default
      }

      const priceResponse = await RobinhoodAPI.getBestBidAsk(this.activeSymbols);
      const symbolPrices = {};

      if (priceResponse && priceResponse.results) {
        priceResponse.results.forEach(result => {
          const price = parseFloat(result.price || result.ask_inclusive_of_buy_spread || 0);
          symbolPrices[result.symbol] = price;
        });
      }

      // Calculate total asset value across all symbols
      let totalAssetValue = 0;
      const symbolValues = {};

      this.activeSymbols.forEach(symbol => {
        const assetCode = symbolConfig.getAssetCode(symbol);
        let assetQuantity = 0;

        // Use provided quantity for primary symbol, or get from holdings
        if (symbol === this.primarySymbol && actualAssetQuantity !== null) {
          assetQuantity = actualAssetQuantity;
        } else if (holdings && holdings.results) {
          const holding = holdings.results.find(h => h.currency && h.currency.code === assetCode);
          if (holding) {
            assetQuantity = parseFloat(holding.quantity) || 0;
          }
        }

        const currentPrice = symbolPrices[symbol] || 0;
        const assetValue = assetQuantity * currentPrice;
        symbolValues[symbol] = { quantity: assetQuantity, value: assetValue, price: currentPrice };
        totalAssetValue += assetValue;
      });

      const totalBalance = cashBalance + totalAssetValue;

      // Cache the actual balance
      this.cachedActualBalance = totalBalance;

      // Log summary
      console.log(`[PORTFOLIO] 💰 Updated cached balance: $${totalBalance.toFixed(2)} (Cash: $${cashBalance.toFixed(2)}, Crypto: $${totalAssetValue.toFixed(2)})`);

      // Log individual symbol values (only if they have value)
      Object.entries(symbolValues).forEach(([symbol, data]) => {
        const assetCode = symbolConfig.getAssetCode(symbol);
        if (data.quantity > 0) {
          console.log(`[PORTFOLIO] 💰   ${assetCode}: ${data.quantity.toFixed(8)} = $${data.value.toFixed(2)} @ $${data.price.toFixed(2)}`);
        }
      });

    } catch (error) {
      console.error(`[PORTFOLIO] ❌ Error updating cached balance:`, error.message);
    }
  }

  // Sync internal position tracking with actual Robinhood holdings
  async syncPositionsWithHoldings(actualAssetQuantity, symbol = null) {
    const targetSymbol = symbol || this.primarySymbol;
    const symbolConfig = await import('../config/SymbolConfig.js').then(m => m.getSymbolConfig());
    const assetCode = symbolConfig.getAssetCode(targetSymbol);
    console.log(`[PORTFOLIO] 🔄 Syncing positions with actual holdings: ${actualAssetQuantity.toFixed(8)} ${assetCode}`);

    // For now, distribute the actual holdings proportionally among strategies that have positions
    const strategiesWithPositions = [];
    this.strategyPositions.forEach((strategyPositions, strategyName) => {
      if (strategyPositions.has(targetSymbol)) {
        const position = strategyPositions.get(targetSymbol);
        if (position.quantity > 0) {
          strategiesWithPositions.push({ name: strategyName, quantity: position.quantity });
        }
      }
    });

    if (strategiesWithPositions.length === 0) {
      // No tracked positions but we have actual asset - assign to most recently active strategy
      console.log(`[PORTFOLIO] ⚠️  Have ${actualAssetQuantity.toFixed(8)} ${assetCode} but no tracked positions`);

      // Find the most recently active strategy (or first enabled strategy)
      let targetStrategy = null;
      let mostRecentTime = 0;

      this.strategyStatus.forEach((status, strategyName) => {
        if (status.lastTradeTime && status.lastTradeTime > mostRecentTime) {
          mostRecentTime = status.lastTradeTime;
          targetStrategy = strategyName;
        }
      });

      // If no strategy has trade history, use the first enabled strategy
      if (!targetStrategy) {
        const enabledStrategies = Array.from(this.strategyAllocations.keys());
        targetStrategy = enabledStrategies[0];
      }

      if (targetStrategy) {
        console.log(`[PORTFOLIO] 🔄 Assigning ${actualAssetQuantity.toFixed(8)} ${assetCode} to ${targetStrategy} (most recent activity)`);

        // Estimate the average purchase price using P&L analysis
        console.log(`[PORTFOLIO] 💡 Estimating average purchase price using P&L analysis...`);
        const estimatedAveragePrice = await this.estimateAveragePriceFromPnL(actualAssetQuantity);

        // Fallback to current price if estimation fails
        const priceToUse = estimatedAveragePrice > 0 ? estimatedAveragePrice : await this.getCurrentPrice();

        // Update the strategy position (multi-currency support)
        const strategyPositions = this.strategyPositions.get(targetStrategy);
        if (strategyPositions && strategyPositions instanceof Map) {
          // Find the symbol for this position (should be primary symbol for single-strategy case)
          const positionSymbol = this.primarySymbol;
          const position = strategyPositions.get(positionSymbol) || {
            quantity: 0,
            averagePrice: 0,
            totalCost: 0,
            unrealizedPnL: 0,
            realizedPnL: 0,
            symbol: positionSymbol,
            lastUpdateTime: Date.now()
          };

          strategyPositions.set(positionSymbol, {
            ...position,
            quantity: actualAssetQuantity,
            averagePrice: priceToUse,
            totalCost: actualAssetQuantity * priceToUse,
            lastUpdateTime: Date.now()
          });

          console.log(`[PORTFOLIO] ✅ Assigned ${actualAssetQuantity.toFixed(8)} ${assetCode} to ${targetStrategy} @ $${priceToUse.toFixed(2)}`);
          console.log(`[PORTFOLIO] 💰 Total cost: $${(actualAssetQuantity * priceToUse).toFixed(2)}`);
        }
      }
      return;
    }

    // Calculate total tracked and redistribute proportionally
    const totalTracked = strategiesWithPositions.reduce((sum, s) => sum + s.quantity, 0);

    strategiesWithPositions.forEach(strategy => {
      const proportion = strategy.quantity / totalTracked;
      const newQuantity = actualAssetQuantity * proportion;

      // Update position in multi-currency structure
      const strategyPositions = this.strategyPositions.get(strategy.name);
      if (strategyPositions && strategyPositions instanceof Map) {
        const positionSymbol = this.primarySymbol;
        const position = strategyPositions.get(positionSymbol);
        if (position) {
          position.quantity = newQuantity;
          strategyPositions.set(positionSymbol, position);
        }
      }

      console.log(`[PORTFOLIO] 📊 Updated ${strategy.name}: ${strategy.quantity.toFixed(8)} → ${newQuantity.toFixed(8)} ${assetCode}`);
    });
  }

  // Cleanup method to remove event listeners and stop strategies
  cleanup() {
    console.log('[PORTFOLIO] 🧹 Cleaning up PortfolioManager...');

    // Stop automatic order reconciliation
    this.stopOrderReconciliation();

    // Remove event listeners
    if (this.tradingEvents) {
      this.tradingEvents.removeAllListeners('tradeReady');
      this.tradingEvents.removeAllListeners('tradeNotReady');
      this.tradingEvents.removeAllListeners('priceUpdate');
    }

    // Clean up individual strategies
    if (this.strategyInstances) {
      this.strategyInstances.forEach((strategy) => {
        if (strategy.cleanup) {
          strategy.cleanup();
        }
      });
      this.strategyInstances.clear();
    }

    console.log('[PORTFOLIO] ✅ PortfolioManager cleanup complete');
  }

  // Update strategy statuses when price updates occur
  async updateStrategyStatusesOnPriceUpdate(priceData) {
    const currentPrice = priceData.price;
    const symbol = priceData.symbol;
    const totalBalance = this.getActualMaxTradingBalanceSync();

    // Update status for each strategy based on current conditions for this specific symbol
    for (const [name, strategy] of this.strategyInstances) {
      const allocation = this.strategyAllocations.get(name) || 0;
      const strategyBalance = totalBalance * allocation;

      // Check if strategy has any open positions (across all symbols)
      const currentPrices = { [symbol]: currentPrice };
      const hasAnyPosition = this.hasAnyPosition(name, currentPrices);
      // Check if strategy has position in this specific symbol
      const hasPositionInSymbol = this.getStrategyPosition(name, symbol).quantity > 0;

      // Handle underfunded strategies
      if (strategyBalance < 5 && !hasAnyPosition) {
        // Only skip if underfunded AND no open position
        // Strategies with positions must be allowed to sell even if underfunded
        this.strategyStatus.set(name, {
          ...this.strategyStatus.get(name),
          lastDecision: false,
          lastDecisionTime: Date.now(),
          lastDecisionReason: `Insufficient allocation: $${strategyBalance.toFixed(2)} (minimum $5 required)`,
          nextSignal: `Need $${(5 - strategyBalance).toFixed(2)} more. Increase max trading balance or reduce number of strategies.`,
          isUnderfunded: true
        });
        return;
      }

      // Special handling for underfunded strategies with positions
      if (strategyBalance < 5 && hasAnyPosition) {
        console.log(`[PORTFOLIO] ⚠️  ${name} is underfunded ($${strategyBalance.toFixed(2)}) but has open position - allowing sell decisions only`);
      }

      try {
        // Get strategy's current decision for this specific symbol
        const symbolPosition = this.getStrategyPosition(name, symbol);
        const decision = strategy.shouldEnterTrade(currentPrice, {
          availableBalance: strategyBalance,
          currentPosition: symbolPosition,
          hasPosition: symbolPosition.quantity > 0,
          symbol: symbol // Pass the symbol to the strategy
        });

        // For underfunded strategies with positions, only allow sell decisions
        if (strategyBalance < 5 && hasAnyPosition && decision.shouldEnter) {
          console.log(`[PORTFOLIO] 🚫 ${name} wants to buy ${symbol} but is underfunded - blocking buy signal`);
          decision.shouldEnter = false;
          decision.reason = `Underfunded: $${strategyBalance.toFixed(2)} < $5 minimum - can only sell existing positions`;
        }

        // Update strategy status with current decision for this symbol
        const currentStatus = this.strategyStatus.get(name) || {};

        // Track per-symbol status
        const symbolStatuses = currentStatus.symbolStatuses || {};
        symbolStatuses[symbol] = {
          lastDecision: decision.shouldEnter,
          lastDecisionTime: Date.now(),
          lastDecisionReason: decision.reason || `${symbol}: No reason provided`,
          nextSignal: await this.getNextSignalDescription(strategy, name, currentPrice, decision, symbol),
          currentPrice: currentPrice,
          assetCode: symbol.split('-')[0]
        };

        // Create rotating status message that includes currency info
        const assetCode = symbol.split('-')[0];
        const rotatingStatus = `${assetCode}: ${decision.reason || 'Monitoring market conditions'}`;

        this.strategyStatus.set(name, {
          ...currentStatus,
          lastDecision: decision.shouldEnter,
          lastDecisionTime: Date.now(),
          lastDecisionReason: rotatingStatus,
          nextSignal: await this.getNextSignalDescription(strategy, name, currentPrice, decision, symbol),
          isUnderfunded: false, // Mark as properly funded
          lastEvaluatedSymbol: symbol, // Track which symbol was last evaluated
          symbolStatuses: symbolStatuses, // Per-symbol status tracking
          currentRotatingSymbol: symbol, // Track current symbol for rotation
          rotationIndex: Object.keys(symbolStatuses).indexOf(symbol) // Track rotation position
        });

        // If strategy wants to enter, emit a trading signal for this symbol
        if (decision.shouldEnter) {
          console.log(`[PORTFOLIO] 🎯 ${name} wants to enter ${symbol} at $${currentPrice.toFixed(2)}: ${decision.reason}`);
          this.tradingEvents.emit('tradeSignal', {
            strategy: name,
            symbol: symbol,
            action: 'buy',
            price: currentPrice,
            reason: decision.reason,
            confidence: decision.confidence || 1.0
          });
        }

      } catch (error) {
        // Handle strategy evaluation errors
        this.strategyStatus.set(name, {
          ...this.strategyStatus.get(name),
          lastDecision: false,
          lastDecisionTime: Date.now(),
          lastDecisionReason: `Evaluation error: ${error.message}`,
          nextSignal: 'Waiting for conditions to stabilize'
        });
      }
    }
  }

  // Handle strategy signaling it's no longer ready
  handleTradeNotReadySignal(event) {
    const { strategyName, reason } = event;
    console.log(`[PORTFOLIO] ❌ ${strategyName} no longer ready: ${reason}`);

    // Update strategy status
    this.strategyStatus.set(strategyName, {
      ...this.strategyStatus.get(strategyName),
      lastDecision: false,
      lastDecisionTime: Date.now(),
      lastDecisionReason: reason,
      nextSignal: 'Waiting for conditions to improve'
    });
  }

  updatePrice(price) {
    // Track the last price for position value calculations
    this.lastPrice = parseFloat(price);

    // Update all strategies with current price
    console.log(`📊 PortfolioManager: Updating ${this.strategyInstances.size} strategies with price $${price}`);
    this.strategyInstances.forEach((strategy, name) => {
      console.log(`  🔄 Updating ${name} with price $${price}`);
      strategy.updatePrice(price);
    });

    // Periodically check and sync positions (every 10th price update)
    if (!this.positionCheckCounter) this.positionCheckCounter = 0;
    this.positionCheckCounter++;

    if (this.positionCheckCounter >= 10) {
      this.positionCheckCounter = 0;
      // Don't await - run in background to avoid blocking price updates
      this.checkAndUpdateFilledOrders().catch(error => {
        console.error(`[PORTFOLIO] ❌ Background position check failed:`, error.message);
      });
    }
    
    // Check if it's time to rebalance
    if (Date.now() - this.lastRebalance > this.config.rebalanceFrequency) {
      this.rebalanceAllocations();
    }

    // Event-driven system: No need to poll strategies for status
    // They will signal when ready via events
  }

  // Event-driven system: Status updates happen via strategy signals
  // No need for polling-based status updates

  // Legacy method for compatibility - Event-driven system handles actual trading
  shouldEnterTrade(_, __, options = {}) {
    const isStatusOnly = options.statusOnly || false;

    // For status-only calls (like initial evaluation), just return no trading
    if (isStatusOnly) {
      console.log(`[PORTFOLIO] 📊 Event-driven system: Status updates handled by strategy signals`);
      return { shouldEnter: false, reason: 'Event-driven system active - strategies signal when ready' };
    }

    // For actual trading calls, the event-driven system handles this
    console.log(`[PORTFOLIO] ⚠️  Legacy shouldEnterTrade called - event-driven system should handle trading`);
    return { shouldEnter: false, reason: 'Event-driven system active - use strategy signals for trading' };
  }

  calculateStopLoss(currentPrice, marketData) {
    // Use the strategy that made the last trade
    const lastTrade = this.getLastTrade();
    if (lastTrade && lastTrade.strategy) {
      const strategy = this.strategyInstances.get(lastTrade.strategy);
      if (strategy) {
        const stopLoss = strategy.calculateStopLoss(currentPrice, marketData);
        return {
          ...stopLoss,
          reason: `${lastTrade.strategy}: ${stopLoss.reason}`
        };
      }
    }
    
    // Fallback to first strategy
    const firstStrategy = this.strategyInstances.values().next().value;
    return firstStrategy.calculateStopLoss(currentPrice, marketData);
  }

  getPositionSize(availableCash, currentPrice, strategyName = null) {
    if (strategyName) {
      const strategy = this.strategyInstances.get(strategyName);
      const allocation = this.strategyAllocations.get(strategyName);
      const strategyBalance = availableCash * allocation;
      
      if (strategy) {
        const positionSize = strategy.getPositionSize(strategyBalance, currentPrice);
        return {
          ...positionSize,
          reason: `${strategyName} (${(allocation * 100).toFixed(1)}% allocation): ${positionSize.reason}`
        };
      }
    }
    
    // Fallback
    const firstStrategy = this.strategyInstances.values().next().value;
    return firstStrategy.getPositionSize(availableCash, currentPrice);
  }

  recordTrade(type, price, quantity, strategyName, symbol = null) {
    const targetSymbol = symbol || this.primarySymbol;
    const assetCode = targetSymbol.split('-')[0];

    // Ensure all values are numbers
    const numericPrice = parseFloat(price) || 0;
    const numericQuantity = parseFloat(quantity) || 0;

    const trade = {
      type,
      price: numericPrice,
      quantity: numericQuantity,
      strategy: strategyName,
      symbol: targetSymbol,
      timestamp: Date.now(),
      value: numericPrice * numericQuantity
    };

    console.log(`[PORTFOLIO] 📊 Recording ${type} trade: ${numericQuantity.toFixed(8)} ${assetCode} at $${numericPrice.toFixed(2)} for ${strategyName}`);

    this.tradeHistory.push(trade);

    // Update strategy position tracking
    this.updateStrategyPosition(strategyName, type, numericPrice, numericQuantity, targetSymbol);

    // Update strategy status with trade information
    if (this.strategyStatus.has(strategyName)) {
      const status = this.strategyStatus.get(strategyName);
      // Use current trade price for the symbol being traded
      const currentPrices = { [targetSymbol]: numericPrice };
      const hasAnyPos = this.hasAnyPosition(strategyName, currentPrices);
      this.strategyStatus.set(strategyName, {
        ...status,
        hasPosition: hasAnyPos,
        lastTradeTime: Date.now(),
        lastTradeType: type,
        lastTradePrice: price,
        lastDecisionReason: `${type.toUpperCase()} executed at $${price.toFixed(2)}`,
        nextSignal: type === 'buy' ? 'Monitoring for exit signals' : 'Waiting for next entry opportunity'
      });
    }

    // Update strategy performance tracking
    this.updateStrategyPerformance(strategyName, trade, type);

    // Save positions after each trade to ensure persistence
    this.savePositionsToFile().catch(error => {
      console.error('[PORTFOLIO] ❌ Failed to save positions after trade:', error.message);
    });

    // Clean up any tiny residual positions after trades
    this.cleanupTinyPositions().catch(error => {
      console.error('[PORTFOLIO] ❌ Failed to cleanup tiny positions after trade:', error.message);
    });

    // Log the updated position for debugging
    const position = this.getStrategyPosition(strategyName, this.primarySymbol);
    if (position && position.quantity > 0) {
      const assetCode = this.primarySymbol.split('-')[0];
      console.log(`[PORTFOLIO] 📊 ${strategyName} position updated: ${position.quantity.toFixed(8)} ${assetCode} @ $${position.averagePrice.toFixed(2)}`);
    }

    // Clear bot stats cache to force fresh data on next UI update
    this.clearBotStatsCache();
  }

  // Save positions to file (called after each trade)
  async savePositionsToFile() {
    try {
      console.log('[PORTFOLIO] 💾 Saving positions to bot-data.json...');

      // Use dynamic import but await it to ensure completion
      const { saveConfig } = await import('../bot.js');
      saveConfig(); // saveConfig is synchronous

      console.log('[PORTFOLIO] ✅ Positions saved to bot-data.json');
    } catch (error) {
      console.error('[PORTFOLIO] ❌ Failed to save positions after trade:', error.message);
      console.error('[PORTFOLIO] 🔍 Error details:', error);

      // Try alternative save method as fallback
      try {
        console.log('[PORTFOLIO] 🔄 Attempting fallback save method...');
        this.savePositionsDirectly();
      } catch (fallbackError) {
        console.error('[PORTFOLIO] ❌ Fallback save also failed:', fallbackError.message);
      }
    }
  }

  // Fallback method to save positions directly to file
  savePositionsDirectly() {
    try {
      const fs = require('fs');
      const path = require('path');
      const CONFIG_FILE = path.join(process.cwd(), 'data', 'bot-data.json');

      // Read existing config
      let config = {};
      if (fs.existsSync(CONFIG_FILE)) {
        const data = fs.readFileSync(CONFIG_FILE, 'utf8');
        config = JSON.parse(data);
      }

      // Save positions using the same logic as bot.js
      const positions = {};
      this.strategyPositions.forEach((strategyPositions, strategyName) => {
        const strategyData = {};
        strategyPositions.forEach((position, symbol) => {
          if (position.quantity > 0) {
            strategyData[symbol] = {
              quantity: position.quantity,
              averagePrice: position.averagePrice,
              totalCost: position.totalCost,
              symbol: position.symbol,
              lastUpdateTime: position.lastUpdateTime
            };
          }
        });

        if (Object.keys(strategyData).length > 0) {
          positions[strategyName] = strategyData;
        }
      });

      // Update config with positions
      config.positions = { positions, savedAt: Date.now() };

      // Write back to file
      fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
      console.log('[PORTFOLIO] ✅ Fallback save completed successfully');

    } catch (error) {
      console.error('[PORTFOLIO] ❌ Fallback save failed:', error.message);
      throw error;
    }
  }

  // Clean up duplicate positions and reconstruct correctly
  cleanupAndReconstructPositions() {
    console.log('[PORTFOLIO] 🧹 Cleaning up duplicate positions and reconstructing correctly...');

    // First, clear all existing positions
    this.strategyPositions.clear();

    // Initialize strategy position maps for all configured strategies
    this.config.strategies.forEach(strategyConfig => {
      this.strategyPositions.set(strategyConfig.name, new Map());
    });

    // Now run the standard reconstruction
    this.reconstructPositionsFromTrades();
  }

  // Reconstruct positions from trade history (recovery tool)
  reconstructPositionsFromTrades() {
    console.log('[PORTFOLIO] 🔄 Reconstructing positions from trade history...');

    if (!this.tradeHistory || this.tradeHistory.length === 0) {
      console.log('[PORTFOLIO] ⚠️ No trade history available for reconstruction');
      return;
    }

    // Clear existing positions
    this.strategyPositions.clear();

    // Initialize strategy position maps
    this.config.strategies.forEach(strategyConfig => {
      this.strategyPositions.set(strategyConfig.name, new Map());
    });

    // Group trades by strategy and symbol
    const tradesByStrategy = {};
    this.tradeHistory.forEach(trade => {
      const strategy = trade.strategy;
      const symbol = trade.symbol || this.primarySymbol;

      // Only process trades that have a valid strategy name
      if (!strategy) {
        console.log(`[PORTFOLIO] ⚠️ Skipping trade without strategy: ${JSON.stringify(trade)}`);
        return;
      }

      if (!tradesByStrategy[strategy]) {
        tradesByStrategy[strategy] = {};
      }
      if (!tradesByStrategy[strategy][symbol]) {
        tradesByStrategy[strategy][symbol] = [];
      }

      tradesByStrategy[strategy][symbol].push(trade);
    });

    console.log(`[PORTFOLIO] 📊 Found trades for strategies: ${Object.keys(tradesByStrategy).join(', ')}`);

    // Reconstruct positions for each strategy/symbol combination
    // Only process strategies that actually have trades
    Object.entries(tradesByStrategy).forEach(([strategyName, symbolTrades]) => {
      console.log(`[PORTFOLIO] 🔄 Processing ${strategyName} with ${Object.keys(symbolTrades).length} symbols`);

      Object.entries(symbolTrades).forEach(([symbol, trades]) => {
        console.log(`[PORTFOLIO] 📈 Processing ${symbol} for ${strategyName}: ${trades.length} trades`);
        let totalQuantity = 0;
        let totalCost = 0;
        let realizedPnL = 0;

        // Sort trades by timestamp
        trades.sort((a, b) => a.timestamp - b.timestamp);

        trades.forEach(trade => {
          if (trade.type === 'buy') {
            totalQuantity += parseFloat(trade.quantity);
            totalCost += parseFloat(trade.value || (trade.price * trade.quantity));
          } else if (trade.type === 'sell') {
            const sellQuantity = parseFloat(trade.quantity);
            const sellValue = parseFloat(trade.value || (trade.price * trade.quantity));

            if (totalQuantity > 0) {
              const avgCost = totalCost / totalQuantity;
              const soldCost = avgCost * sellQuantity;
              realizedPnL += sellValue - soldCost;

              totalQuantity -= sellQuantity;
              totalCost -= soldCost;
            }
          }
        });

        // Create position if we have remaining quantity
        if (totalQuantity > 0) {
          const averagePrice = totalCost / totalQuantity;
          const position = {
            quantity: totalQuantity,
            averagePrice: averagePrice,
            totalCost: totalCost,
            unrealizedPnL: 0, // Will be calculated with current prices
            realizedPnL: realizedPnL,
            symbol: symbol,
            lastUpdateTime: Date.now()
          };

          const strategyPositions = this.strategyPositions.get(strategyName);
          if (strategyPositions) {
            strategyPositions.set(symbol, position);

            const assetCode = symbol.split('-')[0];
            console.log(`[PORTFOLIO] ✅ Reconstructed ${strategyName}: ${totalQuantity.toFixed(8)} ${assetCode} at avg $${averagePrice.toFixed(2)}`);
          }
        }
      });
    });

    // Save the reconstructed positions
    console.log('[PORTFOLIO] 💾 Saving reconstructed positions...');
    this.savePositionsToFile().catch(error => {
      console.error('[PORTFOLIO] ❌ Failed to save reconstructed positions:', error.message);
    });

    console.log('[PORTFOLIO] ✅ Position reconstruction complete!');
  }

  // Check and automatically recover positions if needed
  checkAndRecoverPositions() {
    try {
      console.log('[PORTFOLIO] 🔍 Checking position integrity...');

      // Check if we have any positions
      let hasAnyPositions = false;
      this.strategyPositions.forEach((strategyPositions) => {
        strategyPositions.forEach((position) => {
          if (position.quantity > 0) {
            hasAnyPositions = true;
          }
        });
      });

      // Check if we have trade history
      const hasTradeHistory = this.tradeHistory && this.tradeHistory.length > 0;

      if (!hasAnyPositions && hasTradeHistory) {
        console.log('[PORTFOLIO] 🔄 No positions found but trade history exists - auto-recovering positions...');
        this.cleanupAndReconstructPositions();
      } else if (hasAnyPositions && hasTradeHistory) {
        // Check for duplicate positions
        const duplicateCheck = this.checkForDuplicatePositions();
        if (duplicateCheck.hasDuplicates) {
          console.log('[PORTFOLIO] 🧹 Duplicate positions detected - cleaning up...');
          console.log(`[PORTFOLIO] 📊 Duplicates found: ${duplicateCheck.details.join(', ')}`);
          this.cleanupAndReconstructPositions();
        } else {
          console.log('[PORTFOLIO] ✅ Position integrity check passed');
          // Clean up any tiny residual positions
          this.cleanupTinyPositions().catch(error => {
            console.error('[PORTFOLIO] ❌ Failed to cleanup tiny positions during startup:', error.message);
          });
        }
      } else if (!hasAnyPositions && !hasTradeHistory) {
        console.log('[PORTFOLIO] 📝 No positions or trade history - starting fresh');
      } else {
        console.log('[PORTFOLIO] ✅ Positions exist, no trade history to recover from');
        // Clean up any tiny residual positions
        this.cleanupTinyPositions().catch(error => {
          console.error('[PORTFOLIO] ❌ Failed to cleanup tiny positions during startup:', error.message);
        });
      }
    } catch (error) {
      console.error('[PORTFOLIO] ❌ Position integrity check failed:', error.message);
    }
  }

  // Check for duplicate positions across strategies
  checkForDuplicatePositions() {
    const symbolQuantities = new Map(); // symbol -> total quantity across all strategies
    const symbolStrategies = new Map(); // symbol -> array of strategies holding it
    const duplicates = [];

    this.strategyPositions.forEach((strategyPositions, strategyName) => {
      strategyPositions.forEach((position, symbol) => {
        if (position.quantity > 0) {
          const currentTotal = symbolQuantities.get(symbol) || 0;
          symbolQuantities.set(symbol, currentTotal + position.quantity);

          const strategies = symbolStrategies.get(symbol) || [];
          strategies.push(strategyName);
          symbolStrategies.set(symbol, strategies);
        }
      });
    });

    // Check for symbols held by multiple strategies (potential duplicates)
    symbolStrategies.forEach((strategies, symbol) => {
      if (strategies.length > 1) {
        const totalQuantity = symbolQuantities.get(symbol);
        duplicates.push(`${symbol}: ${totalQuantity.toFixed(8)} across ${strategies.length} strategies (${strategies.join(', ')})`);
      }
    });

    return {
      hasDuplicates: duplicates.length > 0,
      details: duplicates
    };
  }

  // Clear bot stats cache to force fresh data
  clearBotStatsCache() {
    try {
      // Import and call the exported cache clearing function
      import('../bot.js').then(({ clearBotStatsCache }) => {
        clearBotStatsCache();
      }).catch(error => {
        console.warn('[PORTFOLIO] ⚠️  Failed to clear bot stats cache:', error.message);
      });
    } catch (error) {
      console.warn('[PORTFOLIO] ⚠️  Failed to clear bot stats cache:', error.message);
    }
  }

  // Check if position is below Robinhood's minimum sellable amount
  async isPositionBelowMinimumSellable(symbol, quantity) {
    try {
      const symbolConfig = await this.getSymbolConfig();
      if (!symbolConfig) return false;

      const metadata = symbolConfig.getSymbolMetadata(symbol);
      if (!metadata) return false;

      return quantity < metadata.minOrderSize;
    } catch (error) {
      console.warn(`[PORTFOLIO] ⚠️  Failed to check minimum sellable for ${symbol}:`, error.message);
      return false;
    }
  }

  // Get symbol config instance
  async getSymbolConfig() {
    try {
      // Try to get from import cache or create new instance
      if (!this._symbolConfig) {
        const { getSymbolConfig } = await import('../config/SymbolConfig.js');
        this._symbolConfig = getSymbolConfig();
      }
      return this._symbolConfig;
    } catch (error) {
      console.warn('[PORTFOLIO] ⚠️  Failed to get symbol config:', error.message);
      return null;
    }
  }

  // Clean up tiny residual positions across all strategies
  async cleanupTinyPositions() {
    const minQuantityThreshold = 0.00001; // Minimum quantity threshold
    const minValueThreshold = 0.01; // Minimum $0.01 current value threshold
    let cleanedCount = 0;

    // Get current prices for accurate value calculation
    let currentPrices = {};
    try {
      const { RobinhoodAPI } = await import('../robinhoodClient.js');
      const activeSymbols = this.activeSymbols || ['BTC-USD', 'ETH-USD', 'DOGE-USD', 'LTC-USD', 'BCH-USD', 'XRP-USD', 'SOL-USD'];
      const priceResponse = await RobinhoodAPI.getBestBidAsk(activeSymbols);

      if (priceResponse && priceResponse.results) {
        priceResponse.results.forEach(result => {
          currentPrices[result.symbol] = parseFloat(result.price || result.ask_inclusive_of_buy_spread || 0);
        });
      }
    } catch (error) {
      console.warn('[PORTFOLIO] ⚠️  Failed to get current prices for cleanup, using average prices');
    }

    for (const [strategyName, strategyPositions] of this.strategyPositions) {
      if (strategyPositions instanceof Map) {
        for (const [symbol, position] of strategyPositions) {
          if (position.quantity > 0) {
            // Use current market price if available, otherwise fall back to average price
            const currentPrice = currentPrices[symbol] || position.averagePrice;
            const currentValue = position.quantity * currentPrice;

            // Check if position is below minimum sellable amount
            const isBelowMinSellable = await this.isPositionBelowMinimumSellable(symbol, position.quantity);

            // Clean up if quantity is tiny OR current value is less than $0.01
            // BUT don't clean up positions that are below minimum sellable (they're stuck anyway)
            if ((position.quantity < minQuantityThreshold || currentValue < minValueThreshold) && !isBelowMinSellable) {
              console.log(`[PORTFOLIO] 🧹 Cleaning up tiny position: ${strategyName} ${symbol} ${position.quantity.toFixed(12)} (current value: $${currentValue.toFixed(4)} at $${currentPrice.toFixed(2)})`);

              // Set position to zero
              strategyPositions.set(symbol, {
                ...position,
                quantity: 0,
                totalCost: 0,
                averagePrice: 0,
                lastUpdateTime: Date.now()
              });

              cleanedCount++;
            } else if (isBelowMinSellable) {
              console.log(`[PORTFOLIO] ⚠️  Keeping position below minimum sellable: ${strategyName} ${symbol} ${position.quantity.toFixed(12)} (cannot be sold on Robinhood)`);
            }
          }
        }
      }
    }

    if (cleanedCount > 0) {
      console.log(`[PORTFOLIO] ✨ Cleaned up ${cleanedCount} tiny residual positions`);
      this.savePositionsToFile().catch(error => {
        console.error('[PORTFOLIO] ❌ Failed to save positions after cleanup:', error.message);
      });
      this.clearBotStatsCache();
    }

    return cleanedCount;
  }

  // Get current price from Robinhood for this portfolio's primary symbol
  async getCurrentPrice(symbol = null) {
    try {
      const targetSymbol = symbol || this.primarySymbol;

      // Ensure RobinhoodAPI is initialized
      if (!this.robinhoodAPI) {
        await this.initializeRobinhoodAPI();
      }

      if (!this.robinhoodAPI) {
        console.warn(`[PORTFOLIO] ⚠️  RobinhoodAPI not available for ${targetSymbol} price lookup`);
        return 0;
      }

      const bestBidAsk = await this.robinhoodAPI.getBestBidAsk(targetSymbol);
      if (bestBidAsk && bestBidAsk.results && bestBidAsk.results.length > 0) {
        const priceData = bestBidAsk.results[0];
        return parseFloat(priceData.price || priceData.ask_inclusive_of_buy_spread || 0);
      }
      return 0;
    } catch (error) {
      console.warn(`[PORTFOLIO] ⚠️  Failed to get current ${targetSymbol} price:`, error.message);
      return 0;
    }
  }

  // Estimate average purchase price using current market price and P&L data
  async estimateAveragePriceFromPnL(btcQuantity) {
    try {
      // Get current market price
      const currentPrice = await this.getCurrentPrice();
      if (currentPrice === 0) {
        console.warn('[PORTFOLIO] ⚠️  Cannot estimate average price without current market price');
        return 0;
      }

      // Get account data to find total equity and P&L
      const account = await this.robinhoodAPI.getAccount();
      if (account && account.total_equity) {
        const totalEquity = parseFloat(account.total_equity);
        const currentValue = btcQuantity * currentPrice;

        console.log(`[PORTFOLIO] 💡 P&L estimation:`);
        console.log(`  Current market price: $${currentPrice.toFixed(2)}`);
        console.log(`  Current position value: $${currentValue.toFixed(2)}`);
        console.log(`  Total account equity: $${totalEquity.toFixed(2)}`);

        // If equity is less than current value, we have a loss
        // Original cost = current value - unrealized P&L
        // Unrealized P&L = total equity - original cost (assuming only BTC position)
        const unrealizedPnL = totalEquity - currentValue;
        const originalCost = currentValue - unrealizedPnL;
        const averagePrice = originalCost / btcQuantity;

        console.log(`  Calculated unrealized P&L: $${unrealizedPnL.toFixed(2)}`);
        console.log(`  Estimated original cost: $${originalCost.toFixed(2)}`);
        console.log(`  Estimated average price: $${averagePrice.toFixed(2)}`);

        // Sanity check: Average price should be reasonable
        if (averagePrice >= 10000 && averagePrice <= 200000) {
          return averagePrice;
        } else {
          console.warn(`[PORTFOLIO] ⚠️  Estimated average price $${averagePrice.toFixed(2)} seems unreasonable`);
          // Fallback to current price if calculation seems wrong
          return currentPrice;
        }
      }
      return 0;
    } catch (error) {
      console.warn('[PORTFOLIO] ⚠️  Failed to estimate average price from P&L:', error.message);
      return 0;
    }
  }

  // Update individual strategy position tracking
  updateStrategyPosition(strategyName, type, price, quantity, symbol = null) {
    const targetSymbol = symbol || this.primarySymbol;
    const strategyPositions = this.strategyPositions.get(strategyName);
    if (!strategyPositions) return;

    // Get or create position for this symbol
    let position = strategyPositions.get(targetSymbol);
    if (!position) {
      position = {
        quantity: 0,
        averagePrice: 0,
        totalCost: 0,
        unrealizedPnL: 0,
        realizedPnL: 0,
        symbol: targetSymbol,
        lastUpdateTime: Date.now()
      };
    }

    // Ensure all values are numbers
    const currentQuantity = parseFloat(position.quantity) || 0;
    const currentTotalCost = parseFloat(position.totalCost) || 0;
    const currentAveragePrice = parseFloat(position.averagePrice) || 0;
    const tradePrice = parseFloat(price) || 0;
    const tradeQuantity = parseFloat(quantity) || 0;

    if (type === 'buy') {
      // Add to position using weighted average cost
      const newTotalCost = currentTotalCost + (tradePrice * tradeQuantity);
      const newQuantity = currentQuantity + tradeQuantity;
      const newAveragePrice = newQuantity > 0 ? newTotalCost / newQuantity : 0;

      const updatedPosition = {
        ...position,
        quantity: newQuantity,
        averagePrice: newAveragePrice,
        totalCost: newTotalCost,
        symbol: targetSymbol,
        lastUpdateTime: Date.now()
      };

      strategyPositions.set(targetSymbol, updatedPosition);
    } else if (type === 'sell') {
      // Reduce position (realized P&L is tracked separately in performance metrics)
      const soldCost = currentAveragePrice * tradeQuantity;
      const realizedPnL = (tradePrice - currentAveragePrice) * tradeQuantity;

      // Track realized P&L in performance metrics, not in position data
      console.log(`[PORTFOLIO] 💰 Realized P&L for ${strategyName} ${targetSymbol}: $${realizedPnL.toFixed(2)}`);

      let newQuantity = Math.max(0, currentQuantity - tradeQuantity);
      let newTotalCost = Math.max(0, currentTotalCost - soldCost);

      // Clean up tiny residual amounts due to floating point precision errors
      // If quantity is less than 0.00001 (very small), consider it zero
      const minQuantityThreshold = 0.00001;
      if (newQuantity < minQuantityThreshold) {
        console.log(`[PORTFOLIO] 🧹 Cleaning up tiny residual position: ${newQuantity.toFixed(12)} ${targetSymbol} for ${strategyName}`);
        newQuantity = 0;
        newTotalCost = 0;
      }

      const updatedPosition = {
        quantity: newQuantity,
        totalCost: newTotalCost,
        averagePrice: newQuantity > 0 ? currentAveragePrice : 0, // Reset average price if position is zero
        symbol: targetSymbol,
        lastUpdateTime: Date.now()
      };

      strategyPositions.set(targetSymbol, updatedPosition);
    }
  }

  updateStrategyPerformance(strategyName, trade, tradeType) {
    const performance = this.strategyPerformance.get(strategyName);
    if (!performance) return;

    // Initialize individual trades array if it doesn't exist
    if (!performance.individualTrades) {
      performance.individualTrades = [];
    }

    // Record every individual trade (buy or sell)
    performance.individualTrades.push({
      type: tradeType,
      price: trade.price,
      quantity: trade.quantity,
      value: trade.value,
      timestamp: trade.timestamp,
      symbol: trade.symbol
    });

    console.log(`[PORTFOLIO] 📊 Recorded ${tradeType} trade for ${strategyName}: ${performance.individualTrades.length} total trades`);

    // For completed trade pairs (sell trades), calculate performance metrics
    if (tradeType === 'sell') {
      // Find corresponding buy trade
      const buyTrade = this.tradeHistory
        .slice()
        .reverse()
        .find(t => t.type === 'buy' && t.strategy === strategyName && t.symbol === trade.symbol);

      if (buyTrade) {
        const returnPct = (trade.price - buyTrade.price) / buyTrade.price;
        const profit = (trade.price - buyTrade.price) * trade.quantity;

        performance.trades.push({
          buyPrice: buyTrade.price,
          sellPrice: trade.price,
          return: returnPct,
          profit,
          duration: trade.timestamp - buyTrade.timestamp,
          symbol: trade.symbol
        });

        console.log(`[PORTFOLIO] 📈 Completed trade pair for ${strategyName}: ${returnPct > 0 ? 'PROFIT' : 'LOSS'} ${(returnPct * 100).toFixed(2)}%`);

        // Calculate updated metrics
        this.calculatePerformanceMetrics(strategyName);
      }
    }
  }

  calculatePerformanceMetrics(strategyName) {
    const performance = this.strategyPerformance.get(strategyName);
    const trades = performance.trades;

    if (trades.length === 0) {
      performance.totalReturn = 0;
      performance.winRate = 0;
      performance.sharpeRatio = 0;
      performance.lastUpdate = Date.now();
      return;
    }

    // Calculate cumulative return (compound returns, not sum)
    let cumulativeReturn = 1.0; // Start with 100% (1.0)
    trades.forEach(trade => {
      cumulativeReturn *= (1 + trade.return); // Compound each trade return
    });
    performance.totalReturn = cumulativeReturn - 1; // Convert back to percentage (subtract initial 100%)

    // Alternative: Calculate return based on actual P&L vs total cost
    const totalValue = this.getStrategyTotalValue(strategyName);
    if (totalValue.totalCost > 0) {
      const actualReturn = (totalValue.totalValue + totalValue.totalRealizedPnL - totalValue.totalCost) / totalValue.totalCost;
      // Use the more conservative of the two calculations to prevent inflated returns
      if (Math.abs(actualReturn) < Math.abs(performance.totalReturn)) {
        performance.totalReturn = actualReturn;
      }
    }

    // Win rate
    const wins = trades.filter(trade => trade.return > 0).length;
    performance.winRate = wins / trades.length;

    // Simple Sharpe ratio approximation
    const returns = trades.map(trade => trade.return);
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);
    performance.sharpeRatio = stdDev > 0 ? avgReturn / stdDev : 0;

    console.log(`[PORTFOLIO] 📊 Updated metrics for ${strategyName}: Return ${(performance.totalReturn * 100).toFixed(2)}%, Win Rate ${(performance.winRate * 100).toFixed(1)}%`);
    performance.lastUpdate = Date.now();
  }

  rebalanceAllocations() {
    console.log('[PORTFOLIO] 🔄 Rebalancing strategy allocations based on performance...');

    const enabledStrategies = Array.from(this.strategyAllocations.keys());

    if (enabledStrategies.length <= 1) {
      console.log('[PORTFOLIO] 📊 Single or no strategies - no performance rebalancing needed');
      this.normalizeAllocations(); // Ensure single strategy gets 100%
      return;
    }

    const performances = Array.from(this.strategyPerformance.entries())
      .map(([name, perf]) => ({
        name,
        score: this.calculateStrategyScore(perf),
        currentAllocation: this.strategyAllocations.get(name)
      }))
      .sort((a, b) => b.score - a.score);

    console.log('[PORTFOLIO] 📊 Performance scores:');
    performances.forEach(p => {
      console.log(`  ${p.name}: ${p.score.toFixed(3)} (current: ${(p.currentAllocation * 100).toFixed(1)}%)`);
    });

    // Redistribute allocations based on performance
    const totalScore = performances.reduce((sum, p) => sum + Math.max(p.score, 0.1), 0);

    performances.forEach(perf => {
      const baseScore = Math.max(perf.score, 0.1);
      let newAllocation = baseScore / totalScore;

      // Apply min/max constraints
      const minAlloc = this.config.minAllocation;
      const maxAlloc = this.config.maxAllocation;
      newAllocation = Math.max(minAlloc, newAllocation);
      newAllocation = Math.min(maxAlloc, newAllocation);

      this.strategyAllocations.set(perf.name, newAllocation);
    });

    // Normalize allocations to sum to 1
    this.normalizeAllocations();

    this.lastRebalance = Date.now();

    console.log('[PORTFOLIO] ✅ Performance-based rebalancing complete');
    this.logAllocations();
  }

  calculateStrategyScore(performance) {
    if (performance.trades.length === 0) return 1; // Default score for new strategies
    
    // Weighted score based on multiple factors
    const returnWeight = 0.4;
    const winRateWeight = 0.3;
    const sharpeWeight = 0.3;
    
    const normalizedReturn = Math.max(-1, Math.min(2, performance.totalReturn)); // Cap between -100% and 200%
    const normalizedSharpe = Math.max(-2, Math.min(3, performance.sharpeRatio)); // Cap Sharpe ratio
    
    return (normalizedReturn * returnWeight) + 
           (performance.winRate * winRateWeight) + 
           (normalizedSharpe * sharpeWeight);
  }

  normalizeAllocations() {
    const enabledStrategies = Array.from(this.strategyAllocations.keys());

    if (enabledStrategies.length === 0) {
      console.log('[PORTFOLIO] ⚠️  No enabled strategies to normalize');
      return;
    }

    if (enabledStrategies.length === 1) {
      // Single strategy gets 100% allocation
      const strategyName = enabledStrategies[0];
      this.strategyAllocations.set(strategyName, 1.0);
      console.log(`[PORTFOLIO] 📊 Single strategy ${strategyName} gets 100% allocation`);
      return;
    }

    // For multiple strategies, always use equal distribution to avoid allocation sum issues
    const equalAllocation = 1.0 / enabledStrategies.length;
    enabledStrategies.forEach(name => {
      this.strategyAllocations.set(name, equalAllocation);
    });

    console.log(`[PORTFOLIO] 📊 Equal distribution: ${enabledStrategies.length} strategies get ${(equalAllocation * 100).toFixed(1)}% each`);

    // Verify the sum is exactly 1.0
    const total = Array.from(this.strategyAllocations.values()).reduce((sum, alloc) => sum + alloc, 0);
    console.log(`[PORTFOLIO] ✅ Allocation sum verification: ${(total * 100).toFixed(1)}%`);
  }

  logAllocations() {
    console.log('Strategy Allocations:');
    this.strategyAllocations.forEach((allocation, name) => {
      const performance = this.strategyPerformance.get(name);
      console.log(`  ${name}: ${(allocation * 100).toFixed(1)}% (Return: ${(performance.totalReturn * 100).toFixed(1)}%, Win Rate: ${(performance.winRate * 100).toFixed(1)}%)`);
    });
  }

  // Rebalance allocations when strategies are added/removed
  rebalanceAllocationsOnChange(action, strategyName) {
    const enabledStrategies = Array.from(this.strategyAllocations.keys());

    console.log(`[PORTFOLIO] 🔄 Rebalancing allocations after ${action} of ${strategyName}`);
    console.log(`[PORTFOLIO] 📊 Current strategies: ${enabledStrategies.join(', ')}`);

    if (enabledStrategies.length === 0) {
      console.log('[PORTFOLIO] ⚠️  No strategies enabled');
      return;
    }

    if (enabledStrategies.length === 1) {
      // Single strategy gets 100%
      const singleStrategy = enabledStrategies[0];
      this.strategyAllocations.set(singleStrategy, 1.0);
      console.log(`[PORTFOLIO] 📊 Single strategy ${singleStrategy} gets 100% allocation`);
      return;
    }

    if (action === 'add') {
      // New strategy added - redistribute equally among all strategies
      const equalAllocation = 1.0 / enabledStrategies.length;
      enabledStrategies.forEach(name => {
        this.strategyAllocations.set(name, equalAllocation);
      });
      console.log(`[PORTFOLIO] 📊 Equal redistribution: ${enabledStrategies.length} strategies get ${(equalAllocation * 100).toFixed(1)}% each`);

    } else if (action === 'remove') {
      // Strategy removed - redistribute among remaining strategies
      this.normalizeAllocations();
    }

    // Skip constraint enforcement for now - just ensure equal distribution
    // The min/max constraints can cause allocation sum issues

    // Final normalization to ensure sum = 1.0
    this.normalizeAllocations();

    console.log('[PORTFOLIO] ✅ Allocation rebalancing complete');
    this.logAllocations();

    // Verify allocations sum to 1.0
    this.verifyAndFixAllocations();
  }

  // Verify allocations sum to 100% and fix if needed
  verifyAndFixAllocations() {
    const strategies = Array.from(this.strategyAllocations.keys());
    if (strategies.length === 0) return;

    const total = Array.from(this.strategyAllocations.values()).reduce((sum, alloc) => sum + alloc, 0);

    console.log(`[PORTFOLIO] 🔍 Allocation verification: total = ${(total * 100).toFixed(1)}%`);

    if (Math.abs(total - 1.0) > 0.001) { // Allow small floating point errors
      console.log(`[PORTFOLIO] ⚠️  Allocations don't sum to 100% (${(total * 100).toFixed(1)}%) - auto-fixing with equal distribution`);

      // Force equal redistribution
      const equalAllocation = 1.0 / strategies.length;
      strategies.forEach(name => {
        this.strategyAllocations.set(name, equalAllocation);
      });

      console.log(`[PORTFOLIO] ✅ Auto-fixed: ${strategies.length} strategies now get ${(equalAllocation * 100).toFixed(1)}% each`);
      this.logAllocations();

      // Save the corrected allocations
      this.savePositionsToFile();
    } else {
      console.log(`[PORTFOLIO] ✅ Allocations are correct (${(total * 100).toFixed(1)}%)`);
    }
  }

  // Enforce min/max allocation constraints
  enforceAllocationConstraints() {
    const strategies = Array.from(this.strategyAllocations.keys());

    if (strategies.length === 0) return;

    // For multiple strategies, check if min allocation constraint is feasible
    if (strategies.length > 1) {
      const minPossibleAllocation = 1.0 / strategies.length;
      const effectiveMinAlloc = Math.min(this.config.minAllocation, minPossibleAllocation);

      console.log(`[PORTFOLIO] 📊 Allocation constraints: ${strategies.length} strategies, min=${(effectiveMinAlloc * 100).toFixed(1)}%, max=${(this.config.maxAllocation * 100).toFixed(1)}%`);

      // Apply constraints with feasible minimum
      strategies.forEach(name => {
        let allocation = this.strategyAllocations.get(name);
        allocation = Math.max(effectiveMinAlloc, allocation);
        allocation = Math.min(this.config.maxAllocation, allocation);
        this.strategyAllocations.set(name, allocation);
      });
    } else {
      // Single strategy gets 100%
      const singleStrategy = strategies[0];
      this.strategyAllocations.set(singleStrategy, 1.0);
    }
  }

  async getNextSignalDescription(strategy, strategyName, currentPrice, decision, symbol = null) {
    // Generate a description of what the strategy is waiting for
    const symbolDisplay = symbol ? ` (${symbol.split('-')[0]})` : '';
    const targetSymbol = symbol || this.primarySymbol;

    // Check if strategy has a position below minimum sellable amount
    const position = this.getStrategyPosition(strategyName, targetSymbol);
    if (position && position.quantity > 0) {
      try {
        const isBelowMinSellable = await this.isPositionBelowMinimumSellable(targetSymbol, position.quantity);
        if (isBelowMinSellable) {
          const assetCode = targetSymbol.split('-')[0];
          const symbolConfig = await this.getSymbolConfig();
          const metadata = symbolConfig ? symbolConfig.getSymbolMetadata(targetSymbol) : null;
          const minOrderSize = metadata ? metadata.minOrderSize : 'unknown';

          return `⚠️ Position too small to sell: ${position.quantity.toFixed(8)} ${assetCode} (min: ${minOrderSize} ${assetCode})`;
        }
      } catch (error) {
        console.warn(`[PORTFOLIO] ⚠️  Failed to check minimum sellable in status: ${error.message}`);
      }
    }

    if (decision.shouldEnter) {
      return `Ready to enter trade${symbolDisplay}`;
    }

    // Strategy-specific next signal descriptions with detailed conditions
    switch (strategyName) {
      case 'trend-following':
        if (strategy.priceHistory.length < strategy.config.minTrendLookback) {
          return `Accumulating price data (${strategy.priceHistory.length}/${strategy.config.minTrendLookback} required)`;
        }

        // Check specific trend conditions
        const isUptrend = strategy.isUptrend(strategy.priceHistory, strategy.config.minTrendLookback);
        const shortSMA = strategy.calculateSMA(strategy.priceHistory, strategy.config.shortTermPeriod || 5);
        const recentHigh = Math.max(...strategy.priceHistory.slice(-10));
        const highThreshold = recentHigh * (strategy.config.recentHighThreshold || 0.98);

        if (!isUptrend) {
          const longSMA = strategy.calculateSMA(strategy.priceHistory, strategy.config.minTrendLookback);
          return `Waiting for uptrend: price $${currentPrice.toFixed(2)} needs to be above ${strategy.config.minTrendLookback}-period SMA $${longSMA.toFixed(2)}`;
        }

        if (currentPrice < shortSMA) {
          return `Waiting for pullback to end: price $${currentPrice.toFixed(2)} below short-term SMA $${shortSMA.toFixed(2)}`;
        }

        if (currentPrice > highThreshold) {
          return `Waiting for better entry: price $${currentPrice.toFixed(2)} too close to recent high $${recentHigh.toFixed(2)} (threshold: $${highThreshold.toFixed(2)})`;
        }

        return `Monitoring trend conditions: price $${currentPrice.toFixed(2)}`;

      case 'mean-reversion':
        if (strategy.priceHistory.length < (strategy.config.meanPeriod || strategy.config.lookbackPeriod || 20)) {
          const required = strategy.config.meanPeriod || strategy.config.lookbackPeriod || 20;
          return `Building price history (${strategy.priceHistory.length}/${required} required)`;
        }

        const meanPeriod = strategy.config.meanPeriod || strategy.config.lookbackPeriod || 20;
        const sma = strategy.calculateSMA(strategy.priceHistory, meanPeriod);
        const deviationThreshold = strategy.config.deviationThreshold || 2.0;

        if (sma) {
          const deviation = ((sma - currentPrice) / sma * 100);
          const targetDeviation = deviationThreshold * 100;
          const targetPrice = sma * (1 - deviationThreshold / 100);

          if (deviation >= 0) {
            return `Waiting for oversold condition: price $${currentPrice.toFixed(2)} needs to drop ${targetDeviation.toFixed(1)}% below mean $${sma.toFixed(2)} (target: $${targetPrice.toFixed(2)})`;
          } else {
            return `Price above mean: $${currentPrice.toFixed(2)} vs mean $${sma.toFixed(2)} (${Math.abs(deviation).toFixed(1)}% above)`;
          }
        }
        return `Calculating mean from ${meanPeriod} periods`;

      case 'breakout':
        // Use symbol-specific candle data instead of strategy.priceHistory
        try {
          const evaluationSymbol = symbol || 'BTC-USD';
          const candleService = strategy.multiCandleService ?
            strategy.multiCandleService.getServiceForSymbol(evaluationSymbol) :
            strategy.candleService;

          const lookback = strategy.config.breakoutLookback || 20;
          const candles = candleService.getCandles(strategy.config.timeframe || '5m');

          if (candles.length < lookback) {
            return `Accumulating ${evaluationSymbol} price data (${candles.length}/${lookback} required)`;
          }

          const breakoutHigh = candleService.getHighest(strategy.config.timeframe || '5m', lookback);
          const breakoutThreshold = breakoutHigh * 1.01; // 1% above recent high
          const distanceToBreakout = ((breakoutThreshold - currentPrice) / breakoutThreshold * 100);

          return `Waiting for breakout: price $${currentPrice.toFixed(2)} needs to break above $${breakoutThreshold.toFixed(2)} (${distanceToBreakout.toFixed(1)}% to go)`;
        } catch (error) {
          console.log(`[PORTFOLIO] ⚠️ Error getting breakout signal for ${symbol}: ${error.message}`);
          return `Analyzing breakout conditions for ${symbol || 'BTC-USD'}...`;
        }

      case 'moving-average-crossover':
        if (strategy.priceHistory.length < Math.max(strategy.config.shortPeriod || 5, strategy.config.longPeriod || 20)) {
          const required = Math.max(strategy.config.shortPeriod || 5, strategy.config.longPeriod || 20);
          return `Building price history (${strategy.priceHistory.length}/${required} required)`;
        }

        const shortMA = strategy.calculateSMA(strategy.priceHistory, strategy.config.shortPeriod || 5);
        const longMA = strategy.calculateSMA(strategy.priceHistory, strategy.config.longPeriod || 20);

        if (shortMA && longMA) {
          const crossoverDistance = ((shortMA - longMA) / longMA * 100);
          if (shortMA <= longMA) {
            return `Waiting for golden cross: ${strategy.config.shortPeriod || 5}-MA $${shortMA.toFixed(2)} needs to cross above ${strategy.config.longPeriod || 20}-MA $${longMA.toFixed(2)} (${Math.abs(crossoverDistance).toFixed(1)}% below)`;
          } else {
            return `Golden cross active: ${strategy.config.shortPeriod || 5}-MA $${shortMA.toFixed(2)} above ${strategy.config.longPeriod || 20}-MA $${longMA.toFixed(2)} (${crossoverDistance.toFixed(1)}% above)`;
          }
        }
        return 'Calculating moving averages';

      case 'range-trading':
        if (strategy.priceHistory.length < (strategy.config.rangeLookback || 20)) {
          const required = strategy.config.rangeLookback || 20;
          return `Accumulating price data (${strategy.priceHistory.length}/${required} required)`;
        }

        const rangeLookback = strategy.config.rangeLookback || 20;
        const recentPrices = strategy.priceHistory.slice(-rangeLookback);
        const rangeHigh = Math.max(...recentPrices);
        const rangeLow = Math.min(...recentPrices);
        const rangeSize = rangeHigh - rangeLow;
        const supportLevel = rangeLow + (rangeSize * 0.1); // 10% above range low

        return `Monitoring range: support $${supportLevel.toFixed(2)}, resistance $${rangeHigh.toFixed(2)}, current $${currentPrice.toFixed(2)}`;

      case 'simple':
        return `Simple strategy: ready to buy at current price $${currentPrice.toFixed(2)}`;

      case 'buy-the-dip':
        if (strategy.priceHistory.length < Math.max(strategy.config.dipLookback || 5, strategy.config.minTrendLookback || 20)) {
          const required = Math.max(strategy.config.dipLookback || 5, strategy.config.minTrendLookback || 20);
          return `Accumulating price data (${strategy.priceHistory.length}/${required} required)`;
        }

        const dipLookback = strategy.config.dipLookback || 5;
        const dipThreshold = strategy.config.dipThreshold || 0.05;
        const dipPrices = strategy.priceHistory.slice(-dipLookback);
        const maxRecentPrice = Math.max(...dipPrices);
        const currentDrop = ((maxRecentPrice - currentPrice) / maxRecentPrice * 100);
        const neededDrop = (dipThreshold * 100);
        const dipUptrend = strategy.isUptrend(strategy.priceHistory, strategy.config.minTrendLookback || 20);

        if (currentDrop >= neededDrop && dipUptrend) {
          return `Dip opportunity: ${currentDrop.toFixed(1)}% drop from $${maxRecentPrice.toFixed(2)} in uptrend`;
        } else if (currentDrop >= neededDrop && !dipUptrend) {
          return `Dip detected but not in uptrend: ${currentDrop.toFixed(1)}% drop from $${maxRecentPrice.toFixed(2)}`;
        } else {
          return `Waiting for ${neededDrop.toFixed(1)}% dip (current: ${currentDrop.toFixed(1)}% from high $${maxRecentPrice.toFixed(2)})`;
        }

      case 'trailing-entry':
        if (strategy.priceHistory.length < (strategy.config.trailingLookback || 10)) {
          const required = strategy.config.trailingLookback || 10;
          return `Accumulating price data (${strategy.priceHistory.length}/${required} required)`;
        }

        const trailingLookback = strategy.config.trailingLookback || 10;
        const recoveryPercent = strategy.config.recoveryPercent || 0.03;
        const recentLow = Math.min(...strategy.priceHistory.slice(-trailingLookback));
        const recoveryTarget = recentLow * (1 + recoveryPercent);

        return `Waiting for ${(recoveryPercent * 100).toFixed(1)}% recovery from low $${recentLow.toFixed(2)} (target: $${recoveryTarget.toFixed(2)}, current: $${currentPrice.toFixed(2)})`;

      case 'volatility-weighted':
        if (strategy.priceHistory.length < (strategy.config.minTrendLookback || 20)) {
          const required = strategy.config.minTrendLookback || 20;
          return `Accumulating price data (${strategy.priceHistory.length}/${required} required)`;
        }

        const volatilityUptrend = strategy.isUptrend(strategy.priceHistory, strategy.config.minTrendLookback || 20);
        const riskFactor = strategy.config.riskFactor || 0.02;

        if (volatilityUptrend) {
          return `Uptrend detected: ready to enter with ${(riskFactor * 100).toFixed(1)}% risk-adjusted position size`;
        } else {
          return `Waiting for uptrend confirmation (risk factor: ${(riskFactor * 100).toFixed(1)}%)`;
        }

      default:
        return `Monitoring market conditions at $${currentPrice.toFixed(2)}`;
    }
  }

  getLastTrade() {
    return this.tradeHistory[this.tradeHistory.length - 1];
  }

  // Get position information for a specific strategy and symbol
  getStrategyPosition(strategyName, symbol = null) {
    const targetSymbol = symbol || this.primarySymbol;
    const strategyPositions = this.strategyPositions.get(strategyName);

    if (!strategyPositions || !strategyPositions.has(targetSymbol)) {
      return {
        quantity: 0,
        averagePrice: 0,
        totalCost: 0,
        unrealizedPnL: 0,
        realizedPnL: 0,
        symbol: targetSymbol,
        lastUpdateTime: Date.now()
      };
    }

    const position = strategyPositions.get(targetSymbol);

    // Ensure all numeric values are properly converted to numbers
    // Note: realizedPnL and unrealizedPnL are calculated dynamically, not stored in position
    return {
      quantity: parseFloat(position.quantity) || 0,
      averagePrice: parseFloat(position.averagePrice) || 0,
      totalCost: parseFloat(position.totalCost) || 0,
      symbol: position.symbol || targetSymbol,
      lastUpdateTime: position.lastUpdateTime || Date.now()
    };
  }

  // Get all positions for a specific strategy across all symbols
  getStrategyPositions(strategyName) {
    const strategyPositions = this.strategyPositions.get(strategyName);
    if (!strategyPositions) {
      return new Map();
    }
    return strategyPositions;
  }

  // Get detailed position breakdown for a strategy
  getDetailedPositions(strategyName, currentPrices = {}) {
    const positions = this.getStrategyPositions(strategyName);
    const detailed = [];

    // Get realized P&L for this strategy from performance metrics
    const performance = this.strategyPerformance.get(strategyName);
    const strategyRealizedPnL = performance && performance.trades
      ? performance.trades.reduce((sum, trade) => sum + (trade.profit || 0), 0)
      : 0;

    positions.forEach((position, symbol) => {
      if (position.quantity > 0) {
        const assetCode = symbol.split('-')[0];
        const currentPrice = currentPrices[symbol] || 0;
        const currentValue = position.quantity * currentPrice;
        const unrealizedPnL = currentValue - position.totalCost;

        // Calculate symbol-specific realized P&L (approximate distribution)
        const symbolRealizedPnL = positions.size > 1
          ? strategyRealizedPnL / positions.size  // Distribute evenly across positions
          : strategyRealizedPnL;

        const totalPnL = unrealizedPnL + symbolRealizedPnL;

        detailed.push({
          symbol: symbol,
          assetCode: assetCode,
          quantity: parseFloat(position.quantity.toFixed(8)),
          averagePrice: parseFloat((position.averagePrice || 0).toFixed(2)),
          currentPrice: parseFloat(currentPrice.toFixed(2)),
          totalCost: parseFloat((position.totalCost || 0).toFixed(2)),
          currentValue: parseFloat(currentValue.toFixed(2)),
          unrealizedPnL: parseFloat(unrealizedPnL.toFixed(2)),
          realizedPnL: parseFloat(symbolRealizedPnL.toFixed(2)),
          totalPnL: parseFloat(totalPnL.toFixed(2)),
          lastUpdateTime: position.lastUpdateTime || Date.now()
        });
      }
    });

    return detailed;
  }

  // Get total value of all positions for a strategy
  getStrategyTotalValue(strategyName, currentPrices = {}) {
    const positions = this.getStrategyPositions(strategyName);
    let totalValue = 0;
    let totalCost = 0;
    let activePositionCount = 0;

    positions.forEach((position, symbol) => {
      // Only count positions with actual quantity > 0 AND value >= $0.01
      if (position.quantity > 0) {
        const currentPrice = currentPrices[symbol] || 0;
        const positionValue = position.quantity * currentPrice;

        // Only count positions worth at least $0.01
        if (positionValue >= 0.01) {
          totalValue += positionValue;
          totalCost += position.totalCost;
          activePositionCount++;
        }
      }
    });

    // Calculate realized P&L from performance metrics, not position data
    const performance = this.strategyPerformance.get(strategyName);
    const totalRealizedPnL = performance && performance.trades
      ? performance.trades.reduce((sum, trade) => sum + (trade.profit || 0), 0)
      : 0;

    return {
      totalValue,
      totalCost,
      totalRealizedPnL,
      totalUnrealizedPnL: totalValue - totalCost,
      positionCount: activePositionCount // Only count positions with quantity > 0 AND value >= $0.01
    };
  }

  // Check if strategy has any positions worth at least $0.01
  hasAnyPosition(strategyName, currentPrices = {}) {
    const positions = this.getStrategyPositions(strategyName);
    for (const [symbol, position] of positions) {
      if (position.quantity > 0) {
        const currentPrice = currentPrices[symbol] || 0;
        const positionValue = position.quantity * currentPrice;

        // Only count positions worth at least $0.01
        if (positionValue >= 0.01) {
          return true;
        }
      }
    }
    return false;
  }

  // Get total portfolio position (sum of all strategies)
  getTotalPosition() {
    let totalQuantity = 0;
    let totalCost = 0;

    // Iterate through all strategies and their positions
    this.strategyPositions.forEach((strategyPositions) => {
      // strategyPositions is a Map of symbol -> position
      strategyPositions.forEach((position) => {
        totalQuantity += parseFloat(position.quantity) || 0;
        totalCost += parseFloat(position.totalCost) || 0;
      });
    });

    // Calculate total realized P&L from all strategy performance metrics
    let totalRealizedPnL = 0;
    this.strategyPerformance.forEach((performance) => {
      if (performance.trades) {
        totalRealizedPnL += performance.trades.reduce((sum, trade) => sum + (trade.profit || 0), 0);
      }
    });

    const averagePrice = totalQuantity > 0 ? totalCost / totalQuantity : 0;
    return {
      quantity: totalQuantity,
      averagePrice,
      totalCost,
      realizedPnL: totalRealizedPnL
    };
  }

  // Get total portfolio value (current market value of all positions)
  async getTotalPortfolioValue() {
    try {
      // Get current prices for all active symbols
      const currentPrices = {};

      if (this.robinhoodAPI) {
        // Validate active symbols before API call
        if (!this.activeSymbols || this.activeSymbols.length === 0) {
          console.warn('[PORTFOLIO] ⚠️  No active symbols available for price fetching, using default');
          this.activeSymbols = ['BTC-USD']; // Fallback to default
        }

        const priceResponse = await this.robinhoodAPI.getBestBidAsk(this.activeSymbols);
        if (priceResponse && priceResponse.results) {
          priceResponse.results.forEach(priceData => {
            const symbol = priceData.symbol;
            const price = parseFloat(priceData.price || priceData.ask_inclusive_of_buy_spread || 0);
            if (price > 0) {
              currentPrices[symbol] = price;
            }
          });
        }
      }

      let totalValue = 0;

      // Calculate total value across all strategies and symbols
      this.strategyPositions.forEach((strategyPositions) => {
        strategyPositions.forEach((position, symbol) => {
          const quantity = parseFloat(position.quantity) || 0;
          const currentPrice = currentPrices[symbol] || 0;
          const positionValue = quantity * currentPrice;
          totalValue += positionValue;
        });
      });

      return totalValue;
    } catch (error) {
      console.warn('[PORTFOLIO] ⚠️ Could not calculate total portfolio value:', error.message);
      // Fallback to total cost if current prices unavailable
      return this.getTotalPosition().totalCost;
    }
  }

  // Calculate unrealized P&L dynamically (don't store in position data)
  calculateUnrealizedPnL(strategyName, symbol, currentPrice) {
    const position = this.getStrategyPosition(strategyName, symbol);
    if (position.quantity <= 0) return 0;

    const currentValue = position.quantity * currentPrice;
    const unrealizedPnL = currentValue - position.totalCost;

    return unrealizedPnL;
  }

  // Check if a strategy can enter a new position based on current allocations
  canStrategyTrade(strategyName, requestedAmount, totalAvailableBalance) {
    const allocation = this.strategyAllocations.get(strategyName) || 0;
    const maxAllowedBalance = totalAvailableBalance * allocation;
    const currentPosition = this.getStrategyPosition(strategyName);
    const currentValue = currentPosition.totalCost;

    return (currentValue + requestedAmount) <= maxAllowedBalance;
  }

  // Handle max trading balance changes by reconciling positions
  reconcilePositionsForBalanceChange(newMaxBalance, currentPrice) {
    // Validate inputs
    if (!newMaxBalance || isNaN(newMaxBalance) || newMaxBalance < 0) {
      console.error('[PORTFOLIO] ❌ Invalid newMaxBalance:', newMaxBalance);
      return { action: 'error', message: 'Invalid max balance provided' };
    }

    if (!currentPrice || isNaN(currentPrice) || currentPrice <= 0) {
      console.error('[PORTFOLIO] ❌ Invalid currentPrice:', currentPrice);
      return { action: 'error', message: 'Invalid current price provided' };
    }

    const totalPosition = this.getTotalPosition();

    // Validate total position
    if (!totalPosition || isNaN(totalPosition.quantity) || isNaN(totalPosition.totalCost)) {
      console.error('[PORTFOLIO] ❌ Invalid total position:', totalPosition);
      return { action: 'error', message: 'Invalid position data' };
    }

    const currentTotalValue = totalPosition.quantity * currentPrice;

    // Validate calculated value
    if (isNaN(currentTotalValue)) {
      console.error('[PORTFOLIO] ❌ Calculated total value is NaN:', { quantity: totalPosition.quantity, price: currentPrice });
      return { action: 'error', message: 'Cannot calculate total position value' };
    }

    if (currentTotalValue <= newMaxBalance) {
      // No action needed, current positions fit within new limit
      return { action: 'none', message: 'Current positions fit within new balance limit' };
    }

    // Calculate how much we need to reduce
    const excessValue = currentTotalValue - newMaxBalance;
    const reductionRatio = excessValue / currentTotalValue;

    // Validate calculations
    if (isNaN(excessValue) || isNaN(reductionRatio)) {
      console.error('[PORTFOLIO] ❌ Invalid reduction calculations:', { excessValue, reductionRatio, currentTotalValue, newMaxBalance });
      return { action: 'error', message: 'Cannot calculate position reduction' };
    }

    const reductionPlan = [];

    // Calculate proportional reduction for each strategy
    this.strategyPositions.forEach((strategyPositions, strategyName) => {
      // strategyPositions is a Map of symbol -> position
      strategyPositions.forEach((position, symbol) => {
        const quantity = parseFloat(position.quantity) || 0;
        if (quantity > 0) {
          const positionValue = quantity * currentPrice;
          const reductionAmount = positionValue * reductionRatio;
          const quantityToSell = reductionAmount / currentPrice;

          if (quantityToSell > 0.00001) { // Minimum trade size
            reductionPlan.push({
              strategy: strategyName,
              symbol: symbol,
              currentQuantity: quantity,
              quantityToSell: quantityToSell,
              valueToSell: reductionAmount,
              reason: `Reducing ${symbol} position due to max balance limit change`
            });
          }
        }
      });
    });

    return {
      action: 'reduce',
      message: `Need to reduce positions by $${excessValue.toFixed(2)} to comply with new limit`,
      reductionPlan,
      totalReduction: excessValue
    };
  }

  // Get strategies that need to exit positions (for stop losses or rebalancing)
  getStrategiesNeedingExit(currentPrice, symbol = null) {
    const exitDecisions = [];

    this.strategyInstances.forEach((strategy, name) => {
      // Check all symbols if no specific symbol provided, otherwise check just the specified symbol
      const symbolsToCheck = symbol ? [symbol] : this.activeSymbols;

      symbolsToCheck.forEach(checkSymbol => {
        const position = this.getStrategyPosition(name, checkSymbol);
        const assetCode = checkSymbol.split('-')[0];

        if (position.quantity > 0) {
          // Check if strategy wants to exit this position
          const exitDecision = strategy.shouldExitTrade ?
            strategy.shouldExitTrade(currentPrice, {
              currentPosition: position,
              symbol: checkSymbol
            }) :
            { shouldExit: false, reason: 'No exit logic implemented' };

          if (exitDecision.shouldExit) {
            // Additional safety check: Ensure we don't sell more than the strategy owns
            const quantityToSell = Math.min(
              exitDecision.quantity || position.quantity,
              position.quantity
            );

            if (quantityToSell > 0.********) { // Minimum trade size check
              exitDecisions.push({
                strategy: name,
                decision: exitDecision,
                position,
                quantityToSell: quantityToSell,
                symbol: checkSymbol
              });
              console.log(`🔄 ${name} wants to exit ${checkSymbol}: ${quantityToSell.toFixed(8)} ${assetCode} (has ${position.quantity.toFixed(8)} ${assetCode})`);

              // Update strategy status to show exit decision
              const currentStatus = this.strategyStatus.get(name) || {};
              this.strategyStatus.set(name, {
                ...currentStatus,
                lastDecision: false, // false = exit decision
                lastDecisionTime: Date.now(),
                lastDecisionReason: `Exit ${assetCode}: ${exitDecision.reason}`,
                nextSignal: 'Executing exit order...'
              });

            } else {
              console.warn(`⚠️ ${name} exit decision ignored for ${checkSymbol}: insufficient position (${position.quantity.toFixed(8)} ${assetCode})`);
            }
          }
        }
      });
    });

    return exitDecisions;
  }

  // Manage stop loss orders for individual strategies
  updateStrategyStopLoss(strategyName, orderId, stopPrice) {
    const orders = this.strategyOrders.get(strategyName);
    if (orders) {
      orders.activeStopLoss = {
        orderId,
        stopPrice,
        timestamp: Date.now()
      };
      orders.lastOrderTime = Date.now();
      this.strategyOrders.set(strategyName, orders);
    }
  }

  // Cancel stop loss for a specific strategy
  cancelStrategyStopLoss(strategyName) {
    const orders = this.strategyOrders.get(strategyName);
    if (orders && orders.activeStopLoss) {
      const orderId = orders.activeStopLoss.orderId;
      orders.activeStopLoss = null;
      this.strategyOrders.set(strategyName, orders);
      return orderId;
    }
    return null;
  }

  // Get all active stop loss orders
  getActiveStopLossOrders() {
    const activeOrders = [];
    this.strategyOrders.forEach((orders, strategyName) => {
      if (orders.activeStopLoss) {
        activeOrders.push({
          strategy: strategyName,
          orderId: orders.activeStopLoss.orderId,
          stopPrice: orders.activeStopLoss.stopPrice,
          timestamp: orders.activeStopLoss.timestamp
        });
      }
    });
    return activeOrders;
  }

  // Add a pending order for a strategy
  addPendingOrder(strategyName, orderId, orderType, side, quantity, price = null) {
    const orders = this.strategyOrders.get(strategyName);
    if (orders) {
      const orderInfo = {
        orderId,
        orderType, // 'market', 'limit', 'stop_loss'
        side, // 'buy', 'sell'
        quantity: parseFloat(quantity),
        price: price ? parseFloat(price) : null,
        timestamp: Date.now(),
        status: 'pending'
      };

      orders.pendingOrders.push(orderInfo);
      orders.lastOrderTime = Date.now();
      this.strategyOrders.set(strategyName, orders);

      console.log(`📝 Added pending ${orderType} ${side} order for ${strategyName}: ${quantity} BTC${price ? ` at $${price}` : ''}`);
      return orderInfo;
    }
    return null;
  }

  // Remove a pending order (when filled or cancelled)
  removePendingOrder(strategyName, orderId) {
    const orders = this.strategyOrders.get(strategyName);
    if (orders) {
      const orderIndex = orders.pendingOrders.findIndex(order => order.orderId === orderId);
      if (orderIndex !== -1) {
        const removedOrder = orders.pendingOrders.splice(orderIndex, 1)[0];
        this.strategyOrders.set(strategyName, orders);
        console.log(`✅ Removed pending order for ${strategyName}: ${removedOrder.orderType} ${removedOrder.side}`);
        return removedOrder;
      }
    }
    return null;
  }

  // Get all pending orders for a strategy
  getStrategyPendingOrders(strategyName) {
    const orders = this.strategyOrders.get(strategyName);
    return orders ? orders.pendingOrders : [];
  }

  // Get all pending orders across all strategies
  getAllPendingOrders() {
    const allPendingOrders = [];
    this.strategyOrders.forEach((orders, strategyName) => {
      if (orders.pendingOrders && orders.pendingOrders.length > 0) {
        orders.pendingOrders.forEach(order => {
          allPendingOrders.push({
            ...order,
            strategy: strategyName
          });
        });
      }
    });
    return allPendingOrders;
  }

  // Start automatic order reconciliation (runs every 2 minutes)
  startOrderReconciliation() {
    console.log('[PORTFOLIO] 🔄 Starting automatic order reconciliation system');

    // Run immediately on startup
    setTimeout(() => {
      this.reconcileOrders().catch(error => {
        console.error('[PORTFOLIO] ❌ Initial order reconciliation failed:', error.message);
      });
    }, 5000); // Wait 5 seconds for system to stabilize

    // Then run every 2 minutes
    this.reconciliationInterval = setInterval(() => {
      this.reconcileOrders().catch(error => {
        console.error('[PORTFOLIO] ❌ Order reconciliation failed:', error.message);
      });

      // Also check buying power status periodically
      this.checkBuyingPowerStatus().catch(error => {
        console.error('[PORTFOLIO] ❌ Buying power check failed:', error.message);
      });

      // Periodic position save to prevent data loss
      this.savePositionsToFile().catch(error => {
        console.error('[PORTFOLIO] ❌ Periodic position save failed:', error.message);
      });
    }, 120000); // 2 minutes
  }

  // Stop automatic reconciliation (for cleanup)
  stopOrderReconciliation() {
    if (this.reconciliationInterval) {
      clearInterval(this.reconciliationInterval);
      this.reconciliationInterval = null;
      console.log('[PORTFOLIO] 🛑 Stopped automatic order reconciliation');
    }
  }

  // Display unaccounted holdings information
  displayUnaccountedHoldings() {
    if (!this.unaccountedHoldings || Object.keys(this.unaccountedHoldings).length === 0) {
      return;
    }

    console.log('[PORTFOLIO] 📝 Unaccounted Holdings Summary:');
    console.log('[PORTFOLIO] ⚠️  The following crypto was detected in Robinhood but NOT purchased by the bot:');

    Object.entries(this.unaccountedHoldings).forEach(([symbol, info]) => {
      const assetCode = symbol.split('-')[0];
      const detectedDate = new Date(info.detectedAt).toLocaleString();
      console.log(`[PORTFOLIO]   ${assetCode}: ${info.quantity.toFixed(8)} (detected: ${detectedDate})`);
      console.log(`[PORTFOLIO]     Note: ${info.note}`);
    });

    console.log('[PORTFOLIO] 💡 These holdings will be ignored by all trading strategies');
    console.log('[PORTFOLIO] 💡 The bot will only manage crypto that it purchased through its own trades');
  }

  // Clean up corrupted performance data and position structure
  cleanupCorruptedPerformanceData() {
    console.log('[PORTFOLIO] 🧹 Checking for corrupted performance data and position structure...');

    let cleanedCount = 0;

    // Clean up performance data
    this.strategyPerformance.forEach((performance, strategyName) => {
      // Check for impossible returns (> 1000% or < -100%)
      if (Math.abs(performance.totalReturn) > 10) { // 1000%
        console.log(`[PORTFOLIO] 🗑️  Resetting corrupted performance data for ${strategyName}: ${(performance.totalReturn * 100).toFixed(2)}% return`);

        // Reset performance metrics
        performance.totalReturn = 0;
        performance.winRate = 0;
        performance.sharpeRatio = 0;
        performance.trades = [];
        performance.individualTrades = [];
        performance.lastUpdate = Date.now();

        cleanedCount++;
      }

      // Check for corrupted individual trades with impossible profits
      if (performance.individualTrades) {
        const originalLength = performance.individualTrades.length;
        performance.individualTrades = performance.individualTrades.filter(trade => {
          const tradeValue = trade.price * trade.quantity;
          // Remove trades with impossible values (> $10,000 per trade for small accounts)
          return tradeValue < 10000;
        });

        if (performance.individualTrades.length < originalLength) {
          console.log(`[PORTFOLIO] 🗑️  Removed ${originalLength - performance.individualTrades.length} corrupted trades from ${strategyName}`);
          cleanedCount++;
        }
      }
    });

    // Clean up position data structure - remove P&L from open positions
    this.strategyPositions.forEach((strategyPositions, strategyName) => {
      strategyPositions.forEach((position, symbol) => {
        let positionCleaned = false;

        // Remove realized P&L from open positions (conceptually wrong)
        if (position.realizedPnL !== undefined && position.realizedPnL !== 0) {
          console.log(`[PORTFOLIO] 🗑️  Removing corrupted realized P&L from ${strategyName} ${symbol}: $${position.realizedPnL.toFixed(2)}`);
          delete position.realizedPnL;
          positionCleaned = true;
        }

        // Remove unrealized P&L from open positions (should be calculated dynamically)
        if (position.unrealizedPnL !== undefined) {
          delete position.unrealizedPnL;
          positionCleaned = true;
        }

        // Validate position data
        if (position.totalCost > 10000 || position.averagePrice > 1000000) {
          console.log(`[PORTFOLIO] 🗑️  Removing position with impossible values: ${strategyName} ${symbol}`);
          strategyPositions.delete(symbol);
          positionCleaned = true;
        }

        if (positionCleaned) {
          cleanedCount++;
        }
      });
    });

    if (cleanedCount > 0) {
      console.log(`[PORTFOLIO] ✅ Cleaned corrupted data for ${cleanedCount} items`);
      this.savePositionsToFile();
    } else {
      console.log('[PORTFOLIO] ✅ No corrupted data found');
    }
  }

  // Manual trigger for immediate position reconciliation (can be called from UI/API)
  async forcePositionReconciliation() {
    console.log('[PORTFOLIO] 🔄 Manual position reconciliation triggered');
    try {
      await this.reconcilePositions();
      await this.fixPositionsWithMissingPriceData();
      console.log('[PORTFOLIO] ✅ Manual position reconciliation completed');
      return { success: true, message: 'Position reconciliation completed' };
    } catch (error) {
      console.error('[PORTFOLIO] ❌ Manual position reconciliation failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  // Fix existing positions that have missing or zero price data
  async fixPositionsWithMissingPriceData() {
    console.log('[PORTFOLIO] 🔧 Checking for positions with missing price data and corruption...');

    let fixedCount = 0;
    let cleanedCount = 0;

    for (const [strategyName, strategyPositions] of this.strategyPositions.entries()) {
      if (strategyPositions instanceof Map) {
        for (const [symbol, position] of strategyPositions.entries()) {
          const positionSymbol = position.symbol || symbol;
          const symbolConfig = await import('../config/SymbolConfig.js').then(m => m.getSymbolConfig());
          const assetCode = symbolConfig.getAssetCode(positionSymbol);

          // Check for corrupted dust positions with impossible values
          if (position.quantity > 0 && position.quantity < 0.001 && (position.totalCost > 1000 || position.averagePrice > 1000000)) {
            console.log(`[PORTFOLIO] 🧹 Found corrupted dust position: ${strategyName} ${position.quantity.toFixed(8)} ${assetCode} with impossible values`);
            console.log(`[PORTFOLIO] 🗑️  Removing corrupted dust position to prevent calculation errors`);
            strategyPositions.delete(symbol);
            cleanedCount++;
            continue;
          }

          if (position.quantity > 0 && (position.averagePrice === 0 || position.totalCost === 0)) {
            console.log(`[PORTFOLIO] 🔧 Fixing price data for ${strategyName}: ${position.quantity.toFixed(8)} ${assetCode} @ $0.00`);
            console.log(`[PORTFOLIO] 📊 Current data: avgPrice=$${position.averagePrice}, totalCost=$${position.totalCost}`);

            // Try to estimate average purchase price using P&L analysis
            let priceToUse = await this.estimateAveragePriceFromPnL(position.quantity);

            // If P&L estimation fails, fall back to current market price for this symbol
            if (priceToUse === 0) {
              priceToUse = await this.getCurrentPrice(positionSymbol);
            }

            if (priceToUse > 0) {
              // Update the position with correct price data
              strategyPositions.set(positionSymbol, {
                quantity: position.quantity,
                averagePrice: priceToUse,
                totalCost: position.quantity * priceToUse,
                symbol: positionSymbol, // Ensure symbol is set
                lastUpdateTime: Date.now()
              });

              console.log(`[PORTFOLIO] ✅ Fixed ${strategyName}: ${position.quantity.toFixed(8)} ${assetCode} @ $${priceToUse.toFixed(2)} (cost: $${(position.quantity * priceToUse).toFixed(2)}${resetPnL ? ', P&L reset' : ''})`);
              fixedCount++;
            } else {
              console.warn(`[PORTFOLIO] ⚠️  Could not determine price for ${strategyName} position (${assetCode})`);
            }
          }
        }
      }
    }

    if (fixedCount > 0 || cleanedCount > 0) {
      console.log(`[PORTFOLIO] ✅ Fixed price data for ${fixedCount} positions and cleaned ${cleanedCount} corrupted positions`);
      this.savePositionsToFile();
    } else {
      console.log(`[PORTFOLIO] ✅ All positions have valid price data`);
    }
  }

  // Check and display current buying power status
  async checkBuyingPowerStatus() {
    try {
      const { RobinhoodAPI } = await import('../robinhoodClient.js');
      const account = await RobinhoodAPI.getAccount();

      // Try multiple fields for buying power
      const buyingPowerFields = [
        account?.crypto_buying_power,
        account?.buying_power,
        account?.cash?.total_available,
        account?.cash_available_for_withdrawal,
        account?.cash_balances?.buying_power
      ];

      let buyingPower = 0;
      for (const field of buyingPowerFields) {
        if (field && parseFloat(field) > 0) {
          buyingPower = parseFloat(field);
          break;
        }
      }

      console.log(`[PORTFOLIO] 💰 Current buying power: $${buyingPower.toFixed(2)}`);
      console.log(`[PORTFOLIO] 🔍 DEBUG: Account fields:`, {
        crypto_buying_power: account?.crypto_buying_power,
        buying_power: account?.buying_power,
        cash_total_available: account?.cash?.total_available,
        cash_available_for_withdrawal: account?.cash_available_for_withdrawal
      });

      if (buyingPower < 5) {
        console.log(`[PORTFOLIO] ⚠️ Low buying power: $${buyingPower.toFixed(2)} < $5 minimum for trading`);
        console.log(`[PORTFOLIO] 💡 Consider increasing max trading balance or selling some positions to free up funds`);
      } else {
        console.log(`[PORTFOLIO] ✅ Sufficient buying power available for trading`);
      }

      return buyingPower;
    } catch (error) {
      console.log(`[PORTFOLIO] ❌ Failed to check buying power: ${error.message}`);
      return 0;
    }
  }

  // Reconcile positions with actual Robinhood holdings
  async reconcilePositions() {
    try {
      console.log('[PORTFOLIO] 🔍 Reconciling positions with Robinhood holdings...');

      // Ensure RobinhoodAPI is initialized
      if (!this.robinhoodAPI) {
        await this.initializeRobinhoodAPI();
      }

      if (!this.robinhoodAPI) {
        console.warn('[PORTFOLIO] ⚠️  RobinhoodAPI not available for position reconciliation');
        return;
      }

      // Get actual BTC holdings from Robinhood
      const holdings = await this.robinhoodAPI.getHoldings();
      let actualBtcQuantity = 0;

      if (Array.isArray(holdings)) {
        const btcHolding = holdings.find(h => h.asset_code === 'BTC');
        actualBtcQuantity = btcHolding ? parseFloat(btcHolding.total_quantity || 0) : 0;
      } else if (holdings && holdings.results && Array.isArray(holdings.results)) {
        const btcHolding = holdings.results.find(h => h.asset_code === 'BTC');
        actualBtcQuantity = btcHolding ? parseFloat(btcHolding.total_quantity || 0) : 0;
      }

      // Calculate total tracked BTC positions across all strategies
      let totalTrackedQuantity = 0;
      this.strategyPositions.forEach((strategyPositions) => {
        if (strategyPositions.has(this.primarySymbol)) {
          const position = strategyPositions.get(this.primarySymbol);
          totalTrackedQuantity += parseFloat(position.quantity) || 0;
        }
      });

      console.log(`[PORTFOLIO] 📊 Position comparison:`);
      console.log(`  Robinhood actual: ${actualBtcQuantity.toFixed(8)} BTC`);
      console.log(`  Bot tracked: ${totalTrackedQuantity.toFixed(8)} BTC`);

      const difference = Math.abs(actualBtcQuantity - totalTrackedQuantity);

      if (difference > 0.********) { // More than 1 satoshi difference
        console.log(`[PORTFOLIO] ⚠️  Position mismatch detected: ${difference.toFixed(8)} BTC difference`);

        if (actualBtcQuantity > totalTrackedQuantity) {
          // Robinhood has more BTC than we're tracking - this is unaccounted crypto
          const unaccountedQuantity = actualBtcQuantity - totalTrackedQuantity;
          console.log(`[PORTFOLIO] 📝 Detected unaccounted crypto: ${unaccountedQuantity.toFixed(8)} BTC`);
          console.log(`[PORTFOLIO] 💡 This crypto was not purchased by the bot and will be ignored`);
          console.log(`[PORTFOLIO] ⚠️  Bot will NOT manage this crypto - it may be from manual purchases or transfers`);

          // Track the unaccounted amount but don't assign it to any strategy
          this.unaccountedHoldings = this.unaccountedHoldings || {};
          this.unaccountedHoldings[this.primarySymbol] = {
            quantity: unaccountedQuantity,
            detectedAt: Date.now(),
            note: 'Crypto detected in Robinhood but not purchased by bot'
          };

          // Save the unaccounted holdings info
          this.savePositionsToFile();
          console.log(`[PORTFOLIO] ✅ Unaccounted crypto logged and will be ignored by trading strategies`);

        } else {
          // We're tracking more than Robinhood has
          const excessQuantity = totalTrackedQuantity - actualBtcQuantity;
          console.log(`[PORTFOLIO] ⚠️  Internal tracking shows more BTC than Robinhood: ${excessQuantity.toFixed(8)} BTC excess`);

          // Check if this is just dust (very small amounts below Robinhood's precision)
          if (excessQuantity < 0.********) { // Less than 100 satoshis
            console.log(`[PORTFOLIO] 🧹 Excess amount is dust (${excessQuantity.toFixed(8)} BTC) - cleaning up automatically`);

            // Clean up dust positions across all strategies
            this.strategyPositions.forEach((strategyPositions, strategyName) => {
              if (strategyPositions.has(this.primarySymbol)) {
                const position = strategyPositions.get(this.primarySymbol);
                if (position.quantity > 0 && position.quantity <= excessQuantity) {
                  console.log(`[PORTFOLIO] 🧹 Removing dust position from ${strategyName}: ${position.quantity.toFixed(8)} BTC`);
                  strategyPositions.delete(this.primarySymbol);
                }
              }
            });

            // Save the cleaned positions
            this.savePositionsToFile();
            console.log(`[PORTFOLIO] ✅ Dust positions cleaned up automatically`);

          } else {
            console.log(`[PORTFOLIO] 💡 This could indicate a data inconsistency or failed trade - manual review needed`);
            console.log(`[PORTFOLIO] 💡 Excess amount (${excessQuantity.toFixed(8)} BTC) is too large to be dust`);
          }
        }
      } else {
        console.log(`[PORTFOLIO] ✅ Positions are synchronized (difference: ${difference.toFixed(8)} BTC)`);
      }

    } catch (error) {
      console.error('[PORTFOLIO] ❌ Failed to reconcile positions:', error.message);
    }
  }

  // Automatically reconcile internal tracking with Robinhood reality
  async reconcileOrders() {
    try {
      console.log('[PORTFOLIO] 🔍 Running automatic order and position reconciliation...');

      // First, reconcile positions with actual holdings
      await this.reconcilePositions();

      // Fix any positions with missing price data
      await this.fixPositionsWithMissingPriceData();

      // Then, reconcile pending orders
      const allInternalOrders = this.getAllPendingOrders();

      if (allInternalOrders.length === 0) {
        console.log('[PORTFOLIO] ✅ No internal pending orders to reconcile');
        return;
      }

      console.log(`[PORTFOLIO] 📊 Found ${allInternalOrders.length} internal pending orders to verify`);

      // Get actual orders from Robinhood
      const { RobinhoodAPI } = await import('../robinhoodClient.js');
      const openOrders = await RobinhoodAPI.getOpenOrders('BTC-USD');
      const robinhoodOrderIds = (openOrders?.results || []).map(order => order.id);

      console.log(`[PORTFOLIO] 📊 Found ${robinhoodOrderIds.length} actual orders on Robinhood`);

      // Find orphaned orders (exist in internal tracking but not on Robinhood)
      const orphanedOrders = allInternalOrders.filter(order =>
        !robinhoodOrderIds.includes(order.orderId)
      );

      if (orphanedOrders.length === 0) {
        console.log('[PORTFOLIO] ✅ All internal orders match Robinhood - no cleanup needed');
        return;
      }

      console.log(`[PORTFOLIO] 🧹 Found ${orphanedOrders.length} orphaned orders to clean up`);

      // Clean up orphaned orders
      let cleanedCount = 0;
      for (const order of orphanedOrders) {
        try {
          console.log(`[PORTFOLIO] 🗑️  Removing orphaned ${order.orderType} ${order.side} order for ${order.strategy}: ${order.orderId}`);

          // Remove from internal tracking
          this.removePendingOrder(order.strategy, order.orderId);
          cleanedCount++;

        } catch (error) {
          console.error(`[PORTFOLIO] ❌ Failed to clean up orphaned order ${order.orderId}:`, error.message);
        }
      }

      console.log(`[PORTFOLIO] ✅ Order reconciliation complete: cleaned up ${cleanedCount}/${orphanedOrders.length} orphaned orders`);

    } catch (error) {
      console.error('[PORTFOLIO] ❌ Error during order reconciliation:', error.message);
    }
  }

  // Cancel all pending orders on startup (signals are no longer valid)
  async cancelAllPendingOrdersOnStartup() {
    console.log('[PORTFOLIO] 🔍 Checking for pending orders from previous session...');

    const allPendingOrders = this.getAllPendingOrders();

    if (allPendingOrders.length === 0) {
      console.log('[PORTFOLIO] ✅ No pending orders found from previous session');
      return;
    }

    console.log(`[PORTFOLIO] ⚠️  Found ${allPendingOrders.length} pending orders from previous session - cancelling them (signals no longer valid)`);

    try {
      // Import RobinhoodAPI dynamically to avoid circular dependency
      const { RobinhoodAPI } = await import('../robinhoodClient.js');

      let cancelledCount = 0;
      let failedCount = 0;

      for (const order of allPendingOrders) {
        try {
          console.log(`[PORTFOLIO] 🚫 Cancelling ${order.orderType} ${order.side} order for ${order.strategy}: ${order.orderId}`);

          // Attempt to cancel the order with Robinhood
          const cancelResult = await RobinhoodAPI.cancelOrder(order.orderId);

          if (cancelResult) {
            console.log(`[PORTFOLIO] ✅ Successfully cancelled order ${order.orderId} for ${order.strategy}`);
            cancelledCount++;
          } else {
            console.log(`[PORTFOLIO] ⚠️  Order ${order.orderId} may have already been filled or cancelled`);
            failedCount++;
          }

          // Remove from our tracking regardless of API result
          this.removePendingOrder(order.strategy, order.orderId);

          // Small delay between cancellations to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error) {
          console.error(`[PORTFOLIO] ❌ Failed to cancel order ${order.orderId} for ${order.strategy}:`, error.message);
          failedCount++;

          // Still remove from our tracking since we tried to cancel
          this.removePendingOrder(order.strategy, order.orderId);
        }
      }

      console.log(`[PORTFOLIO] 📊 Startup cleanup complete: ${cancelledCount} cancelled, ${failedCount} failed/already processed`);

    } catch (error) {
      console.error('[PORTFOLIO] ❌ Error during startup order cancellation:', error);

      // Clear all pending orders from our tracking even if API calls failed
      this.strategyOrders.forEach((orders, strategyName) => {
        if (orders.pendingOrders && orders.pendingOrders.length > 0) {
          console.log(`[PORTFOLIO] 🧹 Clearing ${orders.pendingOrders.length} pending orders for ${strategyName} from tracking`);
          orders.pendingOrders = [];
          this.strategyOrders.set(strategyName, orders);
        }
      });
    }
  }

  // Calculate stop loss for a specific strategy
  calculateStrategyStopLoss(strategyName, currentPrice, marketData) {
    const strategy = this.strategyInstances.get(strategyName);
    const position = this.getStrategyPosition(strategyName);

    if (!strategy || position.quantity === 0) {
      return null;
    }

    const stopLossDecision = strategy.calculateStopLoss(currentPrice, {
      ...marketData,
      currentPosition: position
    });

    return {
      ...stopLossDecision,
      strategy: strategyName,
      quantity: position.quantity,
      reason: `${strategyName}: ${stopLossDecision.reason}`
    };
  }

  // Safe strategy disable with position handling
  canDisableStrategy(strategyName) {
    const position = this.getStrategyPosition(strategyName);
    const orders = this.strategyOrders.get(strategyName);

    return {
      canDisable: position.quantity === 0 && (!orders || !orders.activeStopLoss),
      hasPosition: position.quantity > 0,
      positionValue: position.quantity * (position.averagePrice || 0),
      hasActiveOrders: orders && orders.activeStopLoss !== null,
      position: position,
      orders: orders
    };
  }

  // Prepare strategy for safe disable (close positions, cancel orders)
  async prepareStrategyForDisable(strategyName, currentPrice, forceClose = false) {
    const position = this.getStrategyPosition(strategyName);
    const orders = this.strategyOrders.get(strategyName);
    const actions = [];

    // Cancel any active stop loss orders
    if (orders && orders.activeStopLoss) {
      actions.push({
        type: 'cancel_order',
        orderId: orders.activeStopLoss.orderId,
        reason: `Canceling stop loss for strategy disable: ${strategyName}`
      });
    }

    // Handle open position
    if (position.quantity > 0) {
      if (forceClose) {
        actions.push({
          type: 'market_sell',
          strategy: strategyName,
          quantity: position.quantity,
          estimatedValue: position.quantity * currentPrice,
          reason: `Force closing position for strategy disable: ${strategyName}`
        });
      } else {
        actions.push({
          type: 'warning',
          message: `Strategy ${strategyName} has open position: ${position.quantity.toFixed(8)} BTC (value: $${(position.quantity * currentPrice).toFixed(2)})`
        });
      }
    }

    return {
      strategyName,
      canProceed: forceClose || position.quantity === 0,
      actions,
      position,
      orders
    };
  }

  // Execute strategy disable with proper cleanup
  disableStrategy(strategyName, options = {}) {
    const {
      redistributeAllocation = true,
      cleanupPosition = false,
      cleanupOrders = false
    } = options;

    // Get current allocation before removal
    const removedAllocation = this.strategyAllocations.get(strategyName) || 0;

    // Remove strategy from all tracking maps
    this.strategyInstances.delete(strategyName);
    this.strategyAllocations.delete(strategyName);
    this.strategyPerformance.delete(strategyName);
    this.strategyStatus.delete(strategyName);

    // Clean up position and order data if requested
    if (cleanupPosition) {
      this.strategyPositions.delete(strategyName);
    }

    if (cleanupOrders) {
      this.strategyOrders.delete(strategyName);
    }

    // Automatically rebalance allocations to sum to 100%
    if (redistributeAllocation) {
      this.rebalanceAllocationsOnChange('remove', strategyName);
    }

    // Update config to reflect the change
    this.config.strategies = this.config.strategies.filter(s => s.name !== strategyName);

    return {
      success: true,
      removedAllocation,
      redistributedTo: Array.from(this.strategyAllocations.keys()),
      cleanedUpPosition: cleanupPosition,
      cleanedUpOrders: cleanupOrders
    };
  }

  // Get orphaned positions (positions for disabled strategies)
  getOrphanedPositions() {
    const orphaned = [];

    this.strategyPositions.forEach((position, strategyName) => {
      if (!this.strategyInstances.has(strategyName) && position.quantity > 0) {
        orphaned.push({
          strategy: strategyName,
          position,
          isOrphaned: true
        });
      }
    });

    return orphaned;
  }

  // Clean up all orphaned data
  cleanupOrphanedData() {
    const activeStrategies = new Set(this.strategyInstances.keys());
    const cleanedUp = {
      positions: [],
      orders: [],
      performance: [],
      status: []
    };

    // Clean up positions
    this.strategyPositions.forEach((position, name) => {
      if (!activeStrategies.has(name)) {
        cleanedUp.positions.push({ strategy: name, position });
        this.strategyPositions.delete(name);
      }
    });

    // Clean up orders
    this.strategyOrders.forEach((orders, name) => {
      if (!activeStrategies.has(name)) {
        cleanedUp.orders.push({ strategy: name, orders });
        this.strategyOrders.delete(name);
      }
    });

    // Clean up performance data
    this.strategyPerformance.forEach((performance, name) => {
      if (!activeStrategies.has(name)) {
        cleanedUp.performance.push({ strategy: name, performance });
        this.strategyPerformance.delete(name);
      }
    });

    // Clean up status data
    this.strategyStatus.forEach((status, name) => {
      if (!activeStrategies.has(name)) {
        cleanedUp.status.push({ strategy: name, status });
        this.strategyStatus.delete(name);
      }
    });

    return cleanedUp;
  }

  getName() {
    return 'Portfolio Manager';
  }

  getDescription() {
    return `Multi-strategy portfolio manager running ${this.strategyInstances.size} strategies with dynamic allocation based on performance.`;
  }

  async getStats() {
    const stats = {
      strategies: {},
      totalTrades: this.tradeHistory.length,
      rebalanceFrequency: this.config.rebalanceFrequency / (60 * 60 * 1000) + ' hours',
      lastRebalance: new Date(this.lastRebalance).toISOString()
    };

    // Get current prices for all active symbols
    const currentPrices = {};

    // Calculate available trading balance for accurate bucket size display
    const maxTradingBalance = this.getActualMaxTradingBalanceSync();
    const totalPositionValue = await this.getTotalPortfolioValue(currentPrices);
    const availableTradingBalance = Math.max(0, maxTradingBalance - totalPositionValue);

    console.log(`[PORTFOLIO] 📊 Stats calculation: Max=$${maxTradingBalance.toFixed(2)}, Positions=$${totalPositionValue.toFixed(2)}, Available=$${availableTradingBalance.toFixed(2)}`);

    // Try to get prices for all active symbols
    try {
      if (this.robinhoodAPI) {
        // Validate active symbols before API call
        if (!this.activeSymbols || this.activeSymbols.length === 0) {
          console.warn('[PORTFOLIO] ⚠️  No active symbols available for price fetching, using default');
          this.activeSymbols = ['BTC-USD']; // Fallback to default
        }

        const priceResponse = await this.robinhoodAPI.getBestBidAsk(this.activeSymbols);
        if (priceResponse && priceResponse.results) {
          priceResponse.results.forEach(priceData => {
            const symbol = priceData.symbol;
            const price = parseFloat(priceData.price || priceData.ask_inclusive_of_buy_spread || 0);
            if (price > 0) {
              currentPrices[symbol] = price;
            }
          });
          console.log(`[PORTFOLIO] 💡 Fetched current prices for ${Object.keys(currentPrices).length} symbols`);
        }
      }
    } catch (error) {
      console.warn(`[PORTFOLIO] ⚠️  Failed to get current prices:`, error.message);
    }

    // Fallback to cached price for primary symbol if needed
    const currentPrice = currentPrices[this.primarySymbol] || this.lastPrice || 0;
    if (currentPrice > 0 && !currentPrices[this.primarySymbol]) {
      currentPrices[this.primarySymbol] = currentPrice;
    }

    this.strategyInstances.forEach((_, name) => {
      const allocation = this.strategyAllocations.get(name);
      const performance = this.strategyPerformance.get(name);
      const status = this.strategyStatus.get(name);
      const orders = this.strategyOrders.get(name);

      // Get all positions for this strategy across all symbols
      const positions = this.getStrategyPositions(name);
      const totalValue = this.getStrategyTotalValue(name, currentPrices);

      // For backward compatibility, also get primary symbol position
      const primaryPosition = this.getStrategyPosition(name, this.primarySymbol);
      const primaryCurrentPrice = currentPrices[this.primarySymbol] || 0;

      // Calculate strategy-specific available balance
      const strategyAvailableBalance = availableTradingBalance * allocation;

      stats.strategies[name] = {
        allocation: (allocation * 100).toFixed(1) + '%',
        availableBalance: strategyAvailableBalance.toFixed(2),
        trades: performance.individualTrades ? performance.individualTrades.length : 0,
        totalReturn: (performance.totalReturn * 100).toFixed(2) + '%',
        winRate: (performance.winRate * 100).toFixed(1) + '%',
        sharpeRatio: performance.sharpeRatio.toFixed(2),
        position: {
          // For backward compatibility, show primary symbol position
          quantity: primaryPosition.quantity.toFixed(8),
          averagePrice: (primaryPosition.averagePrice || 0).toFixed(2),
          totalCost: (primaryPosition.totalCost || 0).toFixed(2),
          currentValue: (primaryPosition.quantity * primaryCurrentPrice).toFixed(2),
          unrealizedPnL: ((primaryPosition.quantity * primaryCurrentPrice) - (primaryPosition.totalCost || 0)).toFixed(2),
          realizedPnL: (totalValue.totalRealizedPnL || 0).toFixed(2),
          totalPnL: (((primaryPosition.quantity * primaryCurrentPrice) - (primaryPosition.totalCost || 0)) + (totalValue.totalRealizedPnL || 0)).toFixed(2),
          hasPosition: this.hasAnyPosition(name, currentPrices),
          lastUpdateTime: primaryPosition.lastUpdateTime,
          // New multi-currency data
          totalValue: totalValue.totalValue.toFixed(2),
          totalCost: totalValue.totalCost.toFixed(2),
          totalUnrealizedPnL: totalValue.totalUnrealizedPnL.toFixed(2),
          totalRealizedPnL: totalValue.totalRealizedPnL.toFixed(2),
          positionCount: totalValue.positionCount,
          // Detailed position breakdown by symbol
          positions: this.getDetailedPositions(name, currentPrices)
        },
        orders: {
          activeStopLoss: orders?.activeStopLoss ? {
            orderId: orders.activeStopLoss.orderId,
            stopPrice: orders.activeStopLoss.stopPrice.toFixed(2),
            timestamp: orders.activeStopLoss.timestamp
          } : null,
          pendingOrders: orders?.pendingOrders?.map(order => ({
            orderId: order.orderId,
            orderType: order.orderType,
            side: order.side,
            quantity: order.quantity.toFixed(8),
            price: order.price ? order.price.toFixed(2) : null,
            timestamp: order.timestamp,
            status: order.status
          })) || [],
          pendingOrderCount: orders?.pendingOrders?.length || 0,
          lastOrderTime: orders?.lastOrderTime
        },
        status: {
          lastDecision: status?.lastDecision,
          lastDecisionTime: status?.lastDecisionTime,
          lastDecisionReason: status?.lastDecisionReason || 'No recent decisions',
          nextSignal: status?.nextSignal || 'Monitoring market',
          hasPosition: this.hasAnyPosition(name, currentPrices),
          lastTradeTime: status?.lastTradeTime,
          lastTradeType: status?.lastTradeType,
          lastTradePrice: status?.lastTradePrice,
          isUnderfunded: status?.isUnderfunded || false,
          requiredIncrease: status?.requiredIncrease || 0
        }
      };

      // Debug underfunded status
      if (name === 'buy-the-dip' || name === 'volatility-weighted') {
        console.log(`📊 getStats() for ${name}: status.isUnderfunded=${status?.isUnderfunded}, final=${stats.strategies[name].status.isUnderfunded}`);
      }
    });
    
    return stats;
  }
}