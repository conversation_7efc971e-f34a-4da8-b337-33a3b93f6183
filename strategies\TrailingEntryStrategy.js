import { BaseStrategy } from './BaseStrategy.js';

export class TrailingEntryStrategy extends BaseStrategy {
  constructor(config = {}, symbol = 'BTC-USD') {
    super({
      trailingLookback: 10,
      recoveryPercent: 0.03, // 3% recovery from local minimum
      ...config
    }, symbol);
  }

  shouldEnterTrade(currentPrice, marketData) {
    // Get the symbol we're evaluating (from marketData if available, otherwise use default)
    const evaluationSymbol = marketData?.symbol || this.symbol;
    const assetCode = evaluationSymbol.split('-')[0];

    // Don't enter if we already have a position
    if (this.hasPosition(marketData)) {
      const position = marketData.currentPosition;
      return {
        shouldEnter: false,
        reason: `Already holding ${position.quantity.toFixed(8)} ${assetCode} at avg price $${position.averagePrice.toFixed(2)}`
      };
    }

    const { trailingLookback, recoveryPercent } = this.config;

    // Ensure we have enough history to determine a local minimum
    if (this.priceHistory.length < trailingLookback) {
      return {
        shouldEnter: false,
        reason: `Accumulating price data (${this.priceHistory.length}/${trailingLookback} required)`
      };
    }

    const recentPrices = this.priceHistory.slice(-trailingLookback);
    const localLow = Math.min(...recentPrices);
    const recoveryTarget = localLow * (1 + recoveryPercent);
    const hasRecovered = currentPrice >= recoveryTarget;
    const currentRecovery = ((currentPrice - localLow) / localLow * 100);
    const neededRecovery = (recoveryPercent * 100);

    if (hasRecovered) {
      return {
        shouldEnter: true,
        reason: `Recovery confirmed: price $${currentPrice.toFixed(2)} recovered ${currentRecovery.toFixed(1)}% from local low $${localLow.toFixed(2)} (target: ${neededRecovery.toFixed(1)}%)`
      };
    } else {
      const distanceToTarget = ((recoveryTarget - currentPrice) / currentPrice * 100);
      return {
        shouldEnter: false,
        reason: `Waiting for ${neededRecovery.toFixed(1)}% recovery from low $${localLow.toFixed(2)} (target: $${recoveryTarget.toFixed(2)}, current: ${currentRecovery.toFixed(1)}%, ${distanceToTarget.toFixed(1)}% to go)`
      };
    }
  }

  shouldExitTrade(currentPrice, marketData) {
    // Safety check: Don't allow exit if no position exists
    if (!this.hasPosition(marketData)) {
      return {
        shouldExit: false,
        reason: 'Safety check: No position to exit'
      };
    }

    const { lookbackPeriod, pullbackThreshold } = this.config;

    if (this.priceHistory.length < lookbackPeriod + 1) {
      return {
        shouldExit: false,
        reason: `Insufficient data for exit analysis (${this.priceHistory.length}/${lookbackPeriod + 1} required)`
      };
    }

    // Find the highest price since entry (trailing high)
    const recentPrices = this.priceHistory.slice(-lookbackPeriod);
    const trailingHigh = Math.max(...recentPrices);

    // Exit if price pulls back significantly from the trailing high
    const pullbackFromHigh = (trailingHigh - currentPrice) / trailingHigh;
    const exitThreshold = pullbackThreshold * 1.5; // Exit at 1.5x the entry pullback threshold

    if (pullbackFromHigh > exitThreshold) {
      return {
        shouldExit: true,
        reason: `Significant pullback: price $${currentPrice.toFixed(2)} is ${(pullbackFromHigh * 100).toFixed(1)}% below trailing high $${trailingHigh.toFixed(2)} (exit threshold: ${(exitThreshold * 100).toFixed(1)}%)`
      };
    }

    // Also exit if we're in profit and see a smaller pullback (take profits)
    const position = marketData.currentPosition;
    const entryPrice = position.averagePrice;
    const profitPercent = (currentPrice - entryPrice) / entryPrice;

    if (profitPercent > 0.05 && pullbackFromHigh > pullbackThreshold) { // 5% profit + normal pullback (more conservative)
      return {
        shouldExit: true,
        reason: `Taking profits: ${(profitPercent * 100).toFixed(1)}% gain with ${(pullbackFromHigh * 100).toFixed(1)}% pullback from high $${trailingHigh.toFixed(2)}`
      };
    }

    return {
      shouldExit: false,
      reason: `Holding position: price $${currentPrice.toFixed(2)} only ${(pullbackFromHigh * 100).toFixed(1)}% below trailing high $${trailingHigh.toFixed(2)} (exit at ${(exitThreshold * 100).toFixed(1)}%)`
    };
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Enters after a pullback: waits for price to rebound a specified percentage from a local minimum. Useful for capturing reversal moves.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      trailingLookback: {
        type: 'number',
        label: 'Trailing Lookback',
        min: 5,
        max: 50,
        default: 10,
        description: 'Number of periods to determine the local minimum'
      },
      recoveryPercent: {
        type: 'number',
        label: 'Recovery %',
        min: 0.01,
        max: 0.1,
        step: 0.005,
        default: 0.03,
        description: 'Percentage recovery from the local low required to trigger entry'
      }
    };
  }
}
