import http from 'http';
import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
dotenv.config();

import WebSocketPkg from 'ws';
const { Server: WebSocketServer } = WebSocketPkg;

import { startBot, getBotStats, setStrategy, getCurrentStrategy, getSymbolStrategies, getStrategyForSymbol, setMaxTradingBalance, getMaxTradingBalance, saveConfig, getCandleService } from './bot.js';
import { getMultiSymbolCandleService } from './services/MultiSymbolCandleService.js';
import { STRATEGIES, createStrategy } from './strategies/index.js';
import { RobinhoodAPI } from './robinhoodClient.js';
import { getSymbolConfig } from './config/SymbolConfig.js';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';

// Multi-currency system - no default symbol bias
// All currencies are treated equally

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration file path
const CONFIG_FILE = 'data/bot-data.json';

// Centralized function to get active symbols from environment or config
function getActiveSymbols() {
  // First try environment variable
  if (process.env.ACTIVE_SYMBOLS) {
    return process.env.ACTIVE_SYMBOLS.split(',').map(s => s.trim());
  }

  // Fallback to config file
  try {
    if (fs.existsSync(CONFIG_FILE)) {
      const configData = fs.readFileSync(CONFIG_FILE, 'utf8');
      const config = JSON.parse(configData);
      if (config.activeSymbols && Array.isArray(config.activeSymbols)) {
        return config.activeSymbols;
      }
    }
  } catch (error) {
    console.warn('Error reading config file for active symbols:', error.message);
  }

  // Final fallback to default symbols
  return ['BTC-USD', 'ETH-USD', 'DOGE-USD', 'LTC-USD', 'BCH-USD', 'XRP-USD', 'SOL-USD'];
}

// Dynamically discover available strategies
function discoverStrategies() {
  const strategiesPath = path.join(__dirname, 'strategies');
  const strategies = [];
  
  try {
    const files = fs.readdirSync(strategiesPath);
    
    for (const file of files) {
      if (file.endsWith('.js') && file !== 'index.js' && file !== 'BaseStrategy.js') {
        const strategyName = file.replace('.js', '');
        
        // Convert class name to kebab-case
        const id = strategyName
          .replace('Strategy', '')
          .replace(/([A-Z])/g, (match, letter, index) => {
            return index === 0 ? letter.toLowerCase() : '-' + letter.toLowerCase();
          });
        
        try {
          // Dynamically import the strategy to get its schema
          import(`./strategies/${file}`).then(module => {
            const StrategyClass = module[strategyName];
            if (StrategyClass && StrategyClass.prototype) {
              const tempInstance = new StrategyClass();
              
              strategies.push({
                id,
                className: strategyName,
                displayName: tempInstance.getName ? tempInstance.getName().replace('Strategy', '') : strategyName.replace('Strategy', ''),
                description: tempInstance.getDescription ? tempInstance.getDescription() : `${strategyName} trading strategy`,
                config: tempInstance.getConfigSchema ? tempInstance.getConfigSchema() : {}
              });
            }
          }).catch(err => {
            console.warn(`Failed to load strategy ${strategyName}:`, err.message);
          });
        } catch (error) {
          console.warn(`Failed to analyze strategy ${strategyName}:`, error.message);
        }
      }
    }
  } catch (error) {
    console.error('Failed to discover strategies:', error);
  }
  
  return strategies;
}

// Cache discovered strategies
let availableStrategies = [];

// Initialize strategies on startup - make this synchronous and more robust
async function initializeStrategies() {
  const strategiesPath = path.join(__dirname, 'strategies');
  
  try {
    const files = fs.readdirSync(strategiesPath);
    console.log('Found strategy files:', files);
    
    for (const file of files) {
      if (file.endsWith('.js') && file !== 'index.js' && file !== 'BaseStrategy.js' && file !== 'PortfolioManager.js') {
        const strategyName = file.replace('.js', '');
        
        // Convert class name to kebab-case
        const id = strategyName
          .replace('Strategy', '')
          .replace(/([A-Z])/g, (match, letter, index) => {
            return index === 0 ? letter.toLowerCase() : '-' + letter.toLowerCase();
          });
        
        try {
          // Dynamically import the strategy
          const module = await import(`./strategies/${file}`);
          const StrategyClass = module[strategyName];
          
          if (StrategyClass && StrategyClass.prototype) {
            const tempInstance = new StrategyClass();
            
            availableStrategies.push({
              id,
              className: strategyName,
              displayName: tempInstance.getName ? tempInstance.getName().replace('Strategy', '') : strategyName.replace('Strategy', ''),
              description: tempInstance.getDescription ? tempInstance.getDescription() : `${strategyName} trading strategy`,
              config: tempInstance.getConfigSchema ? tempInstance.getConfigSchema() : {}
            });
            
            console.log(`Loaded strategy: ${id} (${strategyName})`);
          }
        } catch (error) {
          console.warn(`Failed to load strategy ${strategyName}:`, error.message);
        }
      }
    }
    
    console.log(`Successfully discovered ${availableStrategies.length} strategies:`, availableStrategies.map(s => s.id));
  } catch (error) {
    console.error('Failed to discover strategies:', error);
  }
}

// Initialize timestamp window discovery immediately on server startup
console.log('🔍 Initializing Robinhood timestamp window...');
try {
  await RobinhoodAPI.initializeTimestampWindow();
  console.log('✅ Timestamp window discovery completed on server startup');
} catch (error) {
  console.error('❌ Failed to discover timestamp window on startup:', error.message);
  console.log('⚠️  Bot will use dynamic fallback timestamp window until discovery succeeds');
}

// Set up periodic timestamp window refresh (every 12 hours)
setInterval(async () => {
  console.log('🔄 Periodic timestamp window refresh...');
  try {
    await RobinhoodAPI.initializeTimestampWindow();
    console.log('✅ Timestamp window refreshed successfully');
  } catch (error) {
    console.error('⚠️  Periodic timestamp window refresh failed:', error.message);
  }
}, 12 * 60 * 60 * 1000); // 12 hours

// Make sure this runs before server starts
await initializeStrategies();

const app = express();
const server = http.createServer(app);
const wss = new WebSocketServer({ server });

// Serve static files from public/
app.use(express.static(path.join(__dirname, 'public')));

// API endpoints MUST come before the fallback route
// Bot control removed - bot now auto-starts and always runs

app.get('/api/portfolio', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    
    // If no strategy is set or it's not a portfolio manager, initialize one
    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      // Initialize default portfolio manager
      const defaultConfig = {
        strategies: [
          { name: 'trend-following', allocation: 0.4, config: { accountPercentToUse: 1.0 } },
          { name: 'mean-reversion', allocation: 0.3, config: { accountPercentToUse: 1.0 } },
          { name: 'breakout', allocation: 0.3, config: { accountPercentToUse: 1.0 } }
        ],
        rebalanceFrequency: 24 * 60 * 60 * 1000
      };
      
      setStrategy('portfolio-manager', defaultConfig);
      const newStrategy = getCurrentStrategy();
      
      if (!newStrategy) {
        return res.status(500).json({ error: 'Failed to initialize portfolio manager' });
      }
    }
    
    const currentStrategy = getCurrentStrategy();
    const stats = await currentStrategy.getStats();
    const allocations = {};
    
    // Get current allocations
    if (currentStrategy.strategyAllocations) {
      currentStrategy.strategyAllocations.forEach((allocation, name) => {
        allocations[name] = allocation;
      });
    }
    
    // Get total available balance and apply max trading balance limit
    const botStats = await getBotStats();
    const actualBalance = parseFloat(botStats.balance || 0) + parseFloat(botStats.btcHoldings || 0) * parseFloat(botStats.btcPrice || 0);
    const maxTradingBalance = getMaxTradingBalance();
    const effectiveBalance = maxTradingBalance !== null ? Math.min(actualBalance, maxTradingBalance) : actualBalance;
    
    res.json({
      strategies: stats.strategies || {},
      allocations,
      totalBalance: effectiveBalance.toFixed(2),
      actualBalance: actualBalance.toFixed(2),
      maxTradingBalance: maxTradingBalance,
      totalTrades: stats.totalTrades || 0,
      lastRebalance: stats.lastRebalance || Date.now(),
      rebalanceFrequency: stats.rebalanceFrequency || '24 hours'
    });
    
  } catch (error) {
    console.error('Portfolio endpoint error:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/portfolio/strategy', express.json(), async (req, res) => {
  const { strategyName, config, enabled } = req.body;
  
  try {
    const strategy = getCurrentStrategy();
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      // Update strategy configuration
      const strategyInstance = strategy.strategyInstances.get(strategyName);
      if (strategyInstance) {
        Object.assign(strategyInstance.config, config);
        
        // Handle enable/disable logic here if needed
        
        res.json({ success: true, message: `Updated ${strategyName} configuration` });
      } else {
        res.status(404).json({ error: 'Strategy not found' });
      }
    } else {
      res.status(500).json({ error: 'Portfolio manager not active' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Allocation rebalancing is now automatic - no manual endpoint needed

app.get('/api/strategies', (req, res) => {
  try {
    console.log(`Serving ${availableStrategies.length} strategies`);
    res.json({
      strategies: availableStrategies,
      count: availableStrategies.length
    });
  } catch (error) {
    console.error('Error serving strategies:', error);
    res.status(500).json({
      error: 'Failed to load strategies',
      strategies: [],
      count: 0
    });
  }
});

// Get available symbols
app.get('/api/symbols', async (req, res) => {
  try {
    const symbolConfig = getSymbolConfig();
    await symbolConfig.initialize();

    const symbols = symbolConfig.getSymbolsForDisplay();
    res.json(symbols);
  } catch (error) {
    console.error('Error getting symbols:', error);
    // Return default symbols if there's an error
    res.json([
      { value: 'BTC-USD', label: 'Bitcoin (BTC-USD)', assetCode: 'BTC', displayName: 'Bitcoin' },
      { value: 'ETH-USD', label: 'Ethereum (ETH-USD)', assetCode: 'ETH', displayName: 'Ethereum' },
      { value: 'DOGE-USD', label: 'Dogecoin (DOGE-USD)', assetCode: 'DOGE', displayName: 'Dogecoin' },
      { value: 'LTC-USD', label: 'Litecoin (LTC-USD)', assetCode: 'LTC', displayName: 'Litecoin' },
      { value: 'BCH-USD', label: 'Bitcoin Cash (BCH-USD)', assetCode: 'BCH', displayName: 'Bitcoin Cash' },
      { value: 'XRP-USD', label: 'XRP (XRP-USD)', assetCode: 'XRP', displayName: 'XRP' }
    ]);
  }
});

app.post('/api/max-balance', express.json(), async (req, res) => {
  const { maxBalance } = req.body;
  
  try {
    const result = setMaxTradingBalance(maxBalance);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/max-balance', async (req, res) => {
  try {
    const maxBalance = getMaxTradingBalance();
    res.json({ maxBalance });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get symbol allocations
app.get('/api/symbol-allocations', (req, res) => {
  try {
    // Load current config
    let config = {};
    if (fs.existsSync(CONFIG_FILE)) {
      const configData = fs.readFileSync(CONFIG_FILE, 'utf8');
      config = JSON.parse(configData);
    }

    // Create default allocations based on active symbols
    const defaultActiveSymbols = getActiveSymbols();
    const defaultAllocations = {};
    const allocation = 1.0 / defaultActiveSymbols.length;
    defaultActiveSymbols.forEach(symbol => {
      defaultAllocations[symbol] = allocation;
    });
    const symbolAllocations = config.symbolAllocations || defaultAllocations;
    const activeSymbols = config.activeSymbols || getActiveSymbols();

    res.json({
      symbolAllocations,
      activeSymbols
    });
  } catch (error) {
    console.error('Error getting symbol allocations:', error);
    res.status(500).json({ error: 'Failed to get symbol allocations' });
  }
});

// Set symbol allocations
app.post('/api/symbol-allocations', express.json(), async (req, res) => {
  try {
    const { symbolAllocations } = req.body;

    if (!symbolAllocations || typeof symbolAllocations !== 'object') {
      return res.status(400).json({ error: 'Invalid symbol allocations' });
    }

    // Validate allocations
    const total = Object.values(symbolAllocations).reduce((sum, alloc) => sum + (parseFloat(alloc) || 0), 0);
    if (Math.abs(total - 1.0) > 0.05) {
      return res.status(400).json({ error: 'Symbol allocations must sum to approximately 100%' });
    }

    // Validate individual allocations
    for (const [symbol, allocation] of Object.entries(symbolAllocations)) {
      const alloc = parseFloat(allocation);
      if (isNaN(alloc) || alloc < 0 || alloc > 1) {
        return res.status(400).json({ error: `Invalid allocation for ${symbol}: ${allocation}` });
      }
    }

    // Load current config
    let config = {};
    if (fs.existsSync(CONFIG_FILE)) {
      const configData = fs.readFileSync(CONFIG_FILE, 'utf8');
      config = JSON.parse(configData);
    }

    // Update symbol allocations
    config.symbolAllocations = symbolAllocations;

    // Save updated config
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));

    console.log(`[API] ✅ Symbol allocations updated:`, symbolAllocations);

    // Update bot's local variable to prevent it from overwriting our changes
    try {
      const { updateSymbolAllocations } = await import('./bot.js');
      updateSymbolAllocations(symbolAllocations);
      console.log(`[API] ✅ Bot's local symbol allocations updated`);
    } catch (error) {
      console.warn(`[API] ⚠️ Failed to update bot's local allocations:`, error.message);
    }

    res.json({
      success: true,
      symbolAllocations,
      message: 'Symbol allocations updated successfully'
    });
  } catch (error) {
    console.error('Error setting symbol allocations:', error);
    res.status(500).json({ error: 'Failed to set symbol allocations' });
  }
});

app.post('/api/portfolio', express.json(), async (req, res) => {
  const { strategies, config = {} } = req.body;
  
  try {
    // Create portfolio manager configuration
    const portfolioConfig = {
      strategies: strategies || [
        {
          name: 'trend-following',
          allocation: 0.4,
          config: { accountPercentToUse: 1.0, minTrendLookback: 20, volatilityLookback: 14 }
        },
        {
          name: 'mean-reversion', 
          allocation: 0.3,
          config: { accountPercentToUse: 1.0 }
        },
        {
          name: 'breakout',
          allocation: 0.3, 
          config: { accountPercentToUse: 1.0 }
        }
      ],
      rebalanceFrequency: config.rebalanceFrequency || 24 * 60 * 60 * 1000, // 24 hours
      performanceWindow: config.performanceWindow || 7 * 24 * 60 * 60 * 1000, // 7 days
      minAllocation: config.minAllocation || 0.1,
      maxAllocation: config.maxAllocation || 0.6,
      rebalanceThreshold: config.rebalanceThreshold || 0.1,
      ...config
    };
    
    const result = setStrategy('portfolio-manager', portfolioConfig);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/portfolio/stats', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      const stats = await strategy.getStats();
      res.json(stats);
    } else {
      res.json({ error: 'Portfolio manager not active' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get detailed position data for a specific strategy
app.get('/api/portfolio/positions/:strategyName', async (req, res) => {
  try {
    const { strategyName } = req.params;
    const strategy = getCurrentStrategy();

    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(400).json({ error: 'Portfolio manager not available' });
    }

    // Get current prices for all active symbols using batch API
    const { RobinhoodAPI } = await import('./robinhoodClient.js');
    const symbolConfig = getSymbolConfig();
    await symbolConfig.initialize();

    // Use strategy's active symbols, with fallback to default symbols
    const activeSymbols = strategy.activeSymbols && strategy.activeSymbols.length > 0
      ? strategy.activeSymbols
      : getActiveSymbols();

    console.log(`[SERVER] 📊 Position details for ${strategyName}: using ${activeSymbols.length} symbols:`, activeSymbols);

    const currentPrices = {};
    try {
      const priceResponse = await RobinhoodAPI.getBestBidAsk(activeSymbols);
      if (priceResponse && priceResponse.results) {
        priceResponse.results.forEach(priceData => {
          const symbol = priceData.symbol;
          const price = parseFloat(priceData.price || priceData.ask_inclusive_of_buy_spread || 0);
          if (price > 0) {
            currentPrices[symbol] = price;
          }
        });
        console.log(`[SERVER] 💡 Fetched current prices for ${Object.keys(currentPrices).length} symbols for position details`);
      }
    } catch (error) {
      console.warn(`[SERVER] ⚠️ Failed to get batch prices for position details:`, error.message);
      // Fallback: set all prices to 0
      activeSymbols.forEach(symbol => {
        currentPrices[symbol] = 0;
      });
    }

    // Get detailed positions for the specific strategy
    const positions = strategy.getDetailedPositions(strategyName, currentPrices);

    // Get stop loss information for each position
    const strategyOrders = strategy.strategyOrders.get(strategyName);
    const stopLossInfo = {};
    if (strategyOrders && strategyOrders.activeStopLoss) {
      stopLossInfo[strategyOrders.activeStopLoss.symbol] = strategyOrders.activeStopLoss.stopPrice;
    }

    // Add stop loss info to positions
    const enrichedPositions = positions.map(position => ({
      ...position,
      stopLoss: stopLossInfo[position.symbol] || null
    }));

    res.json({
      success: true,
      strategyName,
      positions: enrichedPositions
    });
  } catch (error) {
    console.error('Position details error:', error);
    res.status(500).json({ error: error.message });
  }
});

// New endpoint for detailed multi-currency position data
app.get('/api/portfolio/positions', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      const positions = {};

      // Get current prices for all active symbols
      const { RobinhoodAPI } = await import('./robinhoodClient.js');
      const symbolConfig = getSymbolConfig();
      await symbolConfig.initialize();

      const activeSymbols = strategy.activeSymbols || getActiveSymbols();
      const priceResponse = await RobinhoodAPI.getBestBidAsk(activeSymbols);
      const currentPrices = {};

      if (priceResponse && priceResponse.results) {
        priceResponse.results.forEach(result => {
          currentPrices[result.symbol] = parseFloat(result.price || result.ask_inclusive_of_buy_spread || 0);
        });
      }

      // Get detailed positions for each strategy
      strategy.strategyInstances.forEach((_, strategyName) => {
        const strategyPositions = strategy.getStrategyPositions(strategyName);
        const positionDetails = {};

        strategyPositions.forEach((position, symbol) => {
          if (position.quantity > 0) {
            const currentPrice = currentPrices[symbol] || 0;
            const currentValue = position.quantity * currentPrice;
            const unrealizedPnL = currentValue - position.totalCost;

            positionDetails[symbol] = {
              symbol,
              quantity: position.quantity,
              averagePrice: position.averagePrice,
              totalCost: position.totalCost,
              currentPrice,
              currentValue,
              unrealizedPnL,
              realizedPnL: position.realizedPnL,
              totalPnL: unrealizedPnL + position.realizedPnL,
              lastUpdateTime: position.lastUpdateTime,
              assetCode: symbolConfig.getAssetCode(symbol)
            };
          }
        });

        if (Object.keys(positionDetails).length > 0) {
          positions[strategyName] = positionDetails;
        }
      });

      res.json({
        positions,
        currentPrices,
        activeSymbols,
        timestamp: Date.now()
      });
    } else {
      res.json({ error: 'Portfolio manager not active' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/portfolio/status', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      const stats = await strategy.getStats();

      // Get current price for real-time status updates
      const botStats = await getBotStats();
      const currentPrice = parseFloat(botStats.btcPrice || 0);

      // Event-driven system: Use existing strategy statuses
      if (currentPrice > 0) {
        // Get current stats without polling strategies
        const updatedStats = await strategy.getStats();
        res.json({
          ...updatedStats,
          currentPrice: currentPrice,
          lastUpdate: Date.now()
        });
      } else {
        res.json(stats);
      }
    } else {
      res.json({ error: 'Portfolio manager not active' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Diagnostic endpoint to compare internal tracking vs Robinhood reality
app.get('/api/orders/diagnostic', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();

    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(400).json({ error: 'Portfolio manager not active' });
    }

    // Get internal tracking
    const allInternalOrders = strategy.getAllPendingOrders();

    // Get actual Robinhood orders for all symbols
    let robinhoodOrders = [];
    try {
      const openOrders = await RobinhoodAPI.getOpenOrders(); // Get all open orders
      robinhoodOrders = openOrders?.results || [];
    } catch (error) {
      console.error('Error fetching Robinhood orders:', error);
    }

    // Find mismatches
    const internalOrderIds = allInternalOrders.map(o => o.orderId);
    const robinhoodOrderIds = robinhoodOrders.map(o => o.id);

    const onlyInternal = internalOrderIds.filter(id => !robinhoodOrderIds.includes(id));
    const onlyRobinhood = robinhoodOrderIds.filter(id => !internalOrderIds.includes(id));

    res.json({
      internal: {
        count: allInternalOrders.length,
        orders: allInternalOrders
      },
      robinhood: {
        count: robinhoodOrders.length,
        orders: robinhoodOrders.map(o => ({
          id: o.id,
          side: o.side,
          type: o.type,
          quantity: o.asset_quantity,
          state: o.state
        }))
      },
      mismatches: {
        onlyInInternal: onlyInternal.length,
        onlyInRobinhood: onlyRobinhood.length,
        orphanedInternalOrders: onlyInternal,
        unknownRobinhoodOrders: onlyRobinhood
      }
    });

  } catch (error) {
    console.error('Error in diagnostic:', error);
    res.status(500).json({ error: error.message });
  }
});

// Clear pending orders for a specific strategy (manual cleanup)
app.post('/api/portfolio/clear-pending/:strategyName', express.json(), async (req, res) => {
  try {
    const { strategyName } = req.params;
    const strategy = getCurrentStrategy();

    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(400).json({ error: 'Portfolio manager not active' });
    }

    console.log(`[API] 🧹 Manual cleanup requested for ${strategyName} pending orders`);

    // Get pending orders for the strategy
    const pendingOrders = strategy.getStrategyPendingOrders(strategyName);

    if (pendingOrders.length === 0) {
      return res.json({
        success: true,
        message: `No pending orders found for ${strategyName}`,
        clearedCount: 0
      });
    }

    console.log(`[API] 🔍 Found ${pendingOrders.length} pending orders for ${strategyName}`);

    let clearedCount = 0;
    let failedCount = 0;

    // Try to cancel each order with Robinhood
    for (const order of pendingOrders) {
      try {
        console.log(`[API] 🚫 Attempting to cancel order ${order.orderId} for ${strategyName}`);

        const cancelResult = await RobinhoodAPI.cancelOrder(order.orderId);

        if (cancelResult) {
          console.log(`[API] ✅ Successfully cancelled order ${order.orderId}`);
          clearedCount++;
        } else {
          console.log(`[API] ⚠️  Order ${order.orderId} may have already been filled or cancelled`);
          failedCount++;
        }

        // Remove from our tracking regardless of API result
        strategy.removePendingOrder(strategyName, order.orderId);

        // Small delay between cancellations
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        console.error(`[API] ❌ Failed to cancel order ${order.orderId}:`, error.message);
        failedCount++;

        // Still remove from tracking since we tried
        strategy.removePendingOrder(strategyName, order.orderId);
      }
    }

    console.log(`[API] 📊 Cleanup complete for ${strategyName}: ${clearedCount} cancelled, ${failedCount} failed/already processed`);

    res.json({
      success: true,
      message: `Cleared ${pendingOrders.length} pending orders for ${strategyName}`,
      clearedCount: clearedCount,
      failedCount: failedCount,
      totalProcessed: pendingOrders.length
    });

  } catch (error) {
    console.error('[API] ❌ Error clearing pending orders:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get OHLC candle data for specific symbol
app.get('/api/candles/:timeframe', (req, res) => {
  try {
    const { timeframe } = req.params;
    const { period = '1d', symbol = 'BTC-USD' } = req.query;

    // Get multi-symbol candle service
    const multiCandleService = getMultiSymbolCandleService();

    if (!multiCandleService) {
      return res.json({
        candles: [],
        timeframe,
        period,
        symbol,
        count: 0,
        error: 'Multi-symbol candle service not available'
      });
    }

    // Get candles for the requested symbol, timeframe and period
    const candleData = multiCandleService.getChartData(symbol, timeframe, period, true);

    console.log(`[API] Fetching ${timeframe} candles for ${symbol} with ${period} period: ${candleData.length} candles`);

    // Get service stats for debugging
    const serviceStats = multiCandleService.getAllStats();

    res.json({
      candles: candleData,
      timeframe,
      period,
      symbol,
      count: candleData.length,
      source: 'MultiSymbolCandleService',
      serviceStats: {
        activeSymbols: serviceStats.activeSymbols,
        symbolStats: serviceStats.symbolStats[symbol] || null
      }
    });

  } catch (error) {
    console.error('Error getting candle data:', error);
    res.status(500).json({ error: error.message });
  }
});



// Reconstruct positions from trade history (recovery tool)
app.post('/api/portfolio/reconstruct-positions', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(400).json({ error: 'Portfolio manager not available' });
    }

    console.log('[API] 🔄 Position cleanup and reconstruction requested');
    strategy.cleanupAndReconstructPositions();

    res.json({
      success: true,
      message: 'Position reconstruction initiated. Check console for details.'
    });
  } catch (error) {
    console.error('[API] ❌ Position reconstruction failed:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get active symbols configuration
app.get('/api/active-symbols', (req, res) => {
  try {
    const activeSymbols = getActiveSymbols();
    res.json({
      success: true,
      activeSymbols: activeSymbols,
      source: process.env.ACTIVE_SYMBOLS ? 'environment' : 'config/default'
    });
  } catch (error) {
    console.error('Error getting active symbols:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get bot data (including trade history)
app.get('/api/bot-data', async (req, res) => {
  try {
    if (fs.existsSync(CONFIG_FILE)) {
      const configData = fs.readFileSync(CONFIG_FILE, 'utf8');
      const data = JSON.parse(configData);
      res.json({
        success: true,
        data: data
      });
    } else {
      res.json({
        success: true,
        data: {
          tradeHistory: { trades: [] },
          positions: { positions: {} }
        }
      });
    }
  } catch (error) {
    console.error('Error reading bot data:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get portfolio balance history
app.get('/api/portfolio/balance-history', async (req, res) => {
  try {
    const { period = '1d' } = req.query;

    // Get current bot stats for baseline data
    const botStats = await getBotStats();

    // Get actual balance history from server price history and current bot state
    const now = Date.now();
    let cutoffTime;

    switch (period) {
      case '1h':
        cutoffTime = now - (60 * 60 * 1000);
        break;
      case '6h':
        cutoffTime = now - (6 * 60 * 60 * 1000);
        break;
      case '1d':
        cutoffTime = now - (24 * 60 * 60 * 1000);
        break;
      case '3d':
        cutoffTime = now - (3 * 24 * 60 * 60 * 1000);
        break;
      case '1w':
        cutoffTime = now - (7 * 24 * 60 * 60 * 1000);
        break;
      default:
        cutoffTime = now - (24 * 60 * 60 * 1000);
    }

    // Get actual balance history from price data and current holdings
    const balanceHistory = [];
    const currentBalance = parseFloat(botStats.balance) || 0;

    // Get multi-currency holdings and prices
    const currentHoldings = botStats.holdings || {};
    const currentPrices = botStats.prices || {};

    // Calculate total crypto value across all currencies
    let totalCryptoValue = 0;
    Object.keys(currentHoldings).forEach(symbol => {
      const holdings = parseFloat(currentHoldings[symbol]) || 0;
      const price = parseFloat(currentPrices[symbol]) || 0;
      totalCryptoValue += holdings * price;
    });

    const totalValue = currentBalance + totalCryptoValue;

    // Filter server price history to the requested period
    const relevantPriceHistory = serverPriceHistory.filter(point => {
      const timestamp = new Date(point.timestamp).getTime();
      return timestamp >= cutoffTime;
    });

    // If we have price history, calculate balance at each price point
    if (relevantPriceHistory.length > 0) {
      relevantPriceHistory.forEach(pricePoint => {
        const timestamp = new Date(pricePoint.timestamp).getTime();

        // For now, use BTC price for historical calculation (primary symbol)
        // TODO: Enhance to support multi-symbol price history
        const btcPrice = pricePoint.price;
        const btcHoldings = parseFloat(currentHoldings['BTC-USD']) || 0;
        const btcValue = btcHoldings * btcPrice;

        // Add other crypto holdings at current prices (approximation)
        let otherCryptoValue = 0;
        Object.keys(currentHoldings).forEach(symbol => {
          if (symbol !== 'BTC-USD') {
            const holdings = parseFloat(currentHoldings[symbol]) || 0;
            const price = parseFloat(currentPrices[symbol]) || 0;
            otherCryptoValue += holdings * price;
          }
        });

        const totalHoldingsValue = btcValue + otherCryptoValue;
        const portfolioValue = currentBalance + totalHoldingsValue;

        balanceHistory.push({
          timestamp: timestamp,
          cashBalance: currentBalance,
          holdingsValue: totalHoldingsValue,
          totalValue: portfolioValue,
          pnl: portfolioValue - (parseFloat(botStats.portfolioValue) || portfolioValue) // Calculate from starting value
        });
      });
    } else {
      // Fallback: create a single point with current data
      balanceHistory.push({
        timestamp: now,
        cashBalance: currentBalance,
        holdingsValue: totalCryptoValue,
        totalValue: totalValue,
        pnl: parseFloat(botStats.pnl) || 0
      });
    }

    res.json({
      balanceHistory,
      period,
      count: balanceHistory.length,
      currentBalance,
      totalCryptoValue,
      totalValue,
      currentHoldings,
      currentPrices,
      botStats // Include full bot stats for additional context
    });

  } catch (error) {
    console.error('Error getting balance history:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/portfolio/strategy/toggle', express.json(), async (req, res) => {
  const { strategyName, enabled } = req.body;
  
  try {
    const strategy = getCurrentStrategy();
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      
      if (enabled) {
        // Add strategy to portfolio
        const strategyConfig = availableStrategies.find(s => s.id === strategyName);
        if (!strategyConfig) {
          return res.status(404).json({ error: 'Strategy not found' });
        }
        
        // Create default config for the strategy
        const defaultConfig = {};
        Object.entries(strategyConfig.config).forEach(([key, schema]) => {
          defaultConfig[key] = schema.default;
        });
        
        // Add to portfolio with automatic allocation rebalancing
        const currentStrategies = strategy.config.strategies;

        // Add new strategy with temporary allocation (will be rebalanced)
        currentStrategies.push({
          name: strategyName,
          allocation: 0.1, // Temporary allocation
          config: { accountPercentToUse: 1.0, ...defaultConfig }
        });

        // Reinitialize the strategy to pick up the new configuration
        strategy.strategyInstances.set(strategyName, createStrategy(strategyName, { accountPercentToUse: 1.0, ...defaultConfig }));
        strategy.strategyAllocations.set(strategyName, 0.1);
        strategy.strategyPositions.set(strategyName, { quantity: 0, averagePrice: 0, totalCost: 0 });
        strategy.strategyPerformance.set(strategyName, {
          trades: [],
          totalReturn: 0,
          winRate: 0,
          sharpeRatio: 0,
          lastUpdate: Date.now()
        });
        strategy.strategyStatus.set(strategyName, {
          lastDecision: null,
          lastDecisionTime: null,
          lastDecisionReason: 'Strategy enabled, waiting for first price update',
          nextSignal: 'Waiting for market data',
          hasPosition: false,
          lastTradeTime: null,
          lastTradeType: null,
          lastTradePrice: null
        });

        // Automatically rebalance allocations to sum to 100%
        strategy.rebalanceAllocationsOnChange('add', strategyName);

        // Reinitialize portfolio to sync runtime allocations
        strategy.initializeStrategies();

        // Get the actual allocation assigned after rebalancing
        const actualAllocation = strategy.strategyAllocations.get(strategyName) || 0;
        console.log(`Strategy ${strategyName} enabled with ${(actualAllocation * 100).toFixed(1)}% allocation`);
        
      } else {
        // Disable strategy - automatically close positions if they exist
        console.log(`[API] 🔄 Disabling strategy ${strategyName} - will close any open positions automatically`);

        // Check if strategy has positions or orders that need to be closed
        const disableCheck = strategy.canDisableStrategy(strategyName);
        let actionsExecuted = [];

        if (!disableCheck.canDisable) {
          // Strategy has positions/orders - close them automatically
          console.log(`[API] 📋 Strategy ${strategyName} has open positions/orders - closing automatically`);

          // Get current price for position closure calculations
          const symbolPrices = await getMultiSymbolPrices();
          const currentPrice = symbolPrices['BTC-USD'];

          if (!currentPrice) {
            return res.status(500).json({ error: 'Cannot get current price for position calculations' });
          }

          // Prepare strategy for disable (this will close positions and cancel orders)
          const preparation = await strategy.prepareStrategyForDisable(strategyName, currentPrice, true); // Force close positions

          // Execute any required actions (close positions, cancel orders)
          if (preparation.actions && preparation.actions.length > 0) {
            console.log(`[API] 📋 Executing ${preparation.actions.length} actions to disable ${strategyName}`);

            for (const action of preparation.actions) {
              if (action.type === 'market_sell') {
                console.log(`[API] 💰 Closing position: ${action.quantity} BTC for ${strategyName}`);

                const sellResult = await RobinhoodAPI.placeSimpleOrder(
                  uuidv4(),
                  'sell',
                  'market',
                  'BTC-USD',
                  { asset_quantity: action.quantity.toFixed(8) }
                );

                if (sellResult) {
                  // Record the position closure
                  strategy.recordTrade('sell', currentPrice, action.quantity, strategyName);
                  console.log(`[API] ✅ Position closed: ${action.quantity} BTC at $${currentPrice} for strategy: ${strategyName}`);
                  actionsExecuted.push(`Closed position: ${action.quantity.toFixed(8)} BTC at $${currentPrice.toFixed(2)}`);
                } else {
                  console.log(`[API] ❌ Failed to close position for ${strategyName}`);
                  return res.status(500).json({ error: 'Failed to close position while disabling strategy' });
                }
              }

              if (action.type === 'cancel_order') {
                console.log(`[API] 🚫 Canceling order ${action.orderId} for ${strategyName}`);
                await RobinhoodAPI.cancelOrder(action.orderId);
                actionsExecuted.push(`Canceled stop loss order`);
              }
            }
          }
        }

        // Now safely disable the strategy
        const result = strategy.disableStrategy(strategyName, {
          redistributeAllocation: true,
          cleanupPosition: true,
          cleanupOrders: true
        });

        console.log(`[API] ✅ Strategy ${strategyName} disabled, ${(result.removedAllocation * 100).toFixed(1)}% allocation redistributed to remaining strategies`);

        // Include information about actions taken in the response
        const responseMessage = actionsExecuted.length > 0
          ? `Strategy ${strategyName} disabled. Actions taken: ${actionsExecuted.join(', ')}`
          : `Strategy ${strategyName} disabled`;

        // Save configuration
        saveConfig();

        res.json({
          success: true,
          message: responseMessage,
          actionsExecuted: actionsExecuted,
          strategies: strategy.config.strategies
        });
        return;
      }

      // Save configuration
      saveConfig();

      res.json({
        success: true,
        message: `Strategy ${strategyName} ${enabled ? 'enabled' : 'disabled'}`,
        strategies: strategy.config.strategies
      });
      
    } else {
      res.status(500).json({ error: 'Portfolio manager not active' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/portfolio/strategy/config', express.json(), async (req, res) => {
  const { strategyName, config } = req.body;

  try {
    const strategy = getCurrentStrategy();
    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(500).json({ error: 'Portfolio manager not active' });
    }

    // Validate strategy exists in available strategies
    const availableStrategy = availableStrategies.find(s => s.id === strategyName);
    if (!availableStrategy) {
      return res.status(404).json({ error: 'Strategy not found in available strategies' });
    }

    // Validate configuration against schema
    const schema = availableStrategy.config;
    const validatedConfig = {};
    const errors = [];

    // Validate each field in the config
    Object.entries(config).forEach(([key, value]) => {
      const fieldConfig = schema[key];
      if (!fieldConfig) {
        errors.push(`Unknown configuration field: ${key}`);
        return;
      }

      // Type validation
      switch (fieldConfig.type) {
        case 'number':
          const numValue = parseFloat(value);
          if (isNaN(numValue)) {
            errors.push(`${key} must be a number`);
            return;
          }
          if (fieldConfig.min !== undefined && numValue < fieldConfig.min) {
            errors.push(`${key} must be at least ${fieldConfig.min}`);
            return;
          }
          if (fieldConfig.max !== undefined && numValue > fieldConfig.max) {
            errors.push(`${key} must be at most ${fieldConfig.max}`);
            return;
          }
          validatedConfig[key] = numValue;
          break;

        case 'boolean':
          validatedConfig[key] = Boolean(value);
          break;

        default:
          validatedConfig[key] = value;
      }
    });

    if (errors.length > 0) {
      return res.status(400).json({ error: 'Validation failed', details: errors });
    }

    // Update strategy configuration in portfolio config
    const strategyConfig = strategy.config.strategies.find(s => s.name === strategyName);
    if (strategyConfig) {
      Object.assign(strategyConfig.config, validatedConfig);

      // Update the actual strategy instance
      const strategyInstance = strategy.strategyInstances.get(strategyName);
      if (strategyInstance) {
        Object.assign(strategyInstance.config, validatedConfig);
      }

      // Save configuration
      saveConfig();

      console.log(`Updated ${strategyName} configuration:`, validatedConfig);
      res.json({ success: true, message: `Updated ${strategyName} configuration`, config: validatedConfig });
    } else {
      res.status(404).json({ error: 'Strategy not found in portfolio' });
    }

  } catch (error) {
    console.error('Error updating strategy config:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/portfolio/strategy/force-disable', express.json(), async (req, res) => {
  const { strategyName, closePosition = false } = req.body;

  try {
    const strategy = getCurrentStrategy();
    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(500).json({ error: 'Portfolio manager not active' });
    }

    // Get current prices for all symbols for position calculations
    const allSymbols = getActiveSymbols();
    const best = await RobinhoodAPI.getBestBidAsk(allSymbols);

    let symbolPrices = {};
    if (best && best.results && best.results.length > 0) {
      best.results.forEach(priceData => {
        const symbol = priceData.symbol;
        const price = parseFloat(priceData.price || priceData.ask_inclusive_of_buy_spread || '0');
        if (price > 0) {
          symbolPrices[symbol] = price;
        }
      });
    }

    if (Object.keys(symbolPrices).length === 0) {
      return res.status(500).json({ error: 'Cannot get current prices for position calculations' });
    }

    // Prepare strategy for disable
    const preparation = await strategy.prepareStrategyForDisable(strategyName, currentPrice, closePosition);

    if (!preparation.canProceed && !closePosition) {
      return res.status(400).json({
        error: 'Strategy has open positions. Set closePosition=true to force close.',
        preparation
      });
    }

    // Execute actions if closing position
    if (closePosition && preparation.actions.length > 0) {
      for (const action of preparation.actions) {
        if (action.type === 'market_sell') {
          console.log(`Force closing position for ${strategyName}: ${action.quantity} ${action.symbol}`);

          const currentPrice = symbolPrices[action.symbol];
          if (!currentPrice) {
            console.error(`No price available for ${action.symbol}, skipping sell order`);
            continue;
          }

          const sellResult = await RobinhoodAPI.placeOrder({
            symbol: action.symbol,
            clientOrderId: uuidv4(),
            side: 'sell',
            type: 'market',
            config: { asset_quantity: action.quantity.toFixed(8) }
          });

          if (sellResult) {
            // Record the forced sale
            strategy.recordTrade('sell', currentPrice, action.quantity, strategyName);
            console.log(`Forced sale executed: ${action.quantity} BTC at $${currentPrice}`);
          } else {
            return res.status(500).json({ error: 'Failed to execute forced position closure' });
          }
        }

        if (action.type === 'cancel_order') {
          console.log(`Canceling order ${action.orderId} for ${strategyName}`);
          await RobinhoodAPI.cancelOrder(action.orderId);
        }
      }
    }

    // Now safely disable the strategy
    const result = strategy.disableStrategy(strategyName, {
      redistributeAllocation: true,
      cleanupPosition: true,
      cleanupOrders: true
    });

    // Save configuration
    saveConfig();

    res.json({
      success: true,
      message: `Strategy ${strategyName} force disabled`,
      actionsExecuted: preparation.actions,
      result
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/portfolio/orphaned', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(500).json({ error: 'Portfolio manager not active' });
    }

    const orphanedPositions = strategy.getOrphanedPositions();

    res.json({
      orphanedPositions,
      hasOrphaned: orphanedPositions.length > 0,
      count: orphanedPositions.length
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/portfolio/cleanup-orphaned', express.json(), async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(500).json({ error: 'Portfolio manager not active' });
    }

    const cleanedUp = strategy.cleanupOrphanedData();

    // Save configuration after cleanup
    saveConfig();

    res.json({
      success: true,
      message: 'Orphaned data cleaned up',
      cleanedUp
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Endpoint to clean up tiny residual positions
app.post('/api/portfolio/cleanup-positions', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(400).json({ error: 'Portfolio manager not available' });
    }

    console.log('[API] 🧹 Manual position cleanup requested');
    const cleanedCount = await strategy.cleanupTinyPositions();

    res.json({
      success: true,
      message: `Cleaned up ${cleanedCount} tiny residual positions`,
      cleanedCount
    });
  } catch (error) {
    console.error('Position cleanup error:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/strategy/single', express.json(), async (req, res) => {
  const { strategyName, config } = req.body;

  try {
    // Single strategy mode is no longer supported - convert to portfolio with one strategy
    const portfolioConfig = {
      strategies: [
        {
          name: strategyName,
          allocation: 1.0, // 100% allocation for single strategy
          config: config || {}
        }
      ],
      rebalanceFrequency: 24 * 60 * 60 * 1000,
      performanceWindow: 7 * 24 * 60 * 60 * 1000,
      minAllocation: 0.1,
      maxAllocation: 1.0,
      rebalanceThreshold: 0.1
    };

    const result = setStrategy('portfolio-manager', portfolioConfig);

    res.json({
      success: true,
      message: `Created portfolio with single strategy: ${strategyName}`,
      mode: 'portfolio-single',
      strategies: [{ name: strategyName, allocation: 100 }]
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/strategy/portfolio', express.json(), async (req, res) => {
  const { strategies, config } = req.body;
  
  try {
    // Switch back to portfolio mode
    const portfolioConfig = {
      strategies: strategies || [
        {
          name: 'trend-following',
          allocation: 0.5,
          config: { accountPercentToUse: 1.0 }
        },
        {
          name: 'mean-reversion',
          allocation: 0.5,
          config: { accountPercentToUse: 1.0 }
        }
      ],
      rebalanceFrequency: config?.rebalanceFrequency || 24 * 60 * 60 * 1000,
      ...config
    };
    
    const result = setStrategy('portfolio-manager', portfolioConfig);
    
    // Save configuration
    saveConfig();
    
    res.json({ 
      success: true, 
      message: 'Switched to portfolio mode',
      mode: 'portfolio'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/strategy/mode', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();

    // All strategies now run through PortfolioManager
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      const stats = await strategy.getStats();
      const strategyCount = stats.strategies ? Object.keys(stats.strategies).length : 0;

      res.json({
        mode: 'portfolio',
        strategyName: strategy.getName(),
        strategyType: strategy.constructor.name,
        strategyCount: strategyCount,
        isPortfolio: true,
        message: strategyCount === 1 ? 'Portfolio with single strategy' : `Portfolio with ${strategyCount} strategies`
      });
    } else {
      res.json({
        mode: 'error',
        strategyName: strategy ? strategy.getName() : 'None',
        strategyType: strategy ? strategy.constructor.name : 'None',
        isPortfolio: false,
        error: 'Non-PortfolioManager strategy detected - this should not happen'
      });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Force timestamp window re-discovery (for signature format fixes)
app.post('/api/auth/rediscover-timestamp', async (req, res) => {
  try {
    console.log('[API] 🔄 Forcing timestamp window re-discovery...');
    const success = await RobinhoodAPI.forceTimestampRediscovery();

    if (success) {
      res.json({
        success: true,
        message: 'Timestamp window re-discovery completed successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Timestamp window re-discovery failed'
      });
    }
  } catch (error) {
    console.error('[API] ❌ Timestamp re-discovery error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Clean up corrupted position and performance data
app.post('/api/portfolio/cleanup-data', async (req, res) => {
  try {
    console.log('[API] 🧹 Cleaning up corrupted position and performance data...');
    const strategy = getCurrentStrategy();

    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(400).json({
        success: false,
        error: 'Portfolio manager not available'
      });
    }

    // Run the cleanup function
    strategy.cleanupCorruptedPerformanceData();

    res.json({
      success: true,
      message: 'Position and performance data cleanup completed successfully'
    });
  } catch (error) {
    console.error('[API] ❌ Data cleanup error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Fallback route for SPA (index.html) - MUST be last
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/index.html'));
});

// Add memory management for server-side price storage
let serverPriceHistory = [];
const MAX_SERVER_HISTORY = 1000;

// Store WebSocket connections for broadcasting
const wsConnections = new Set();

// Broadcast immediate updates to all connected clients
async function broadcastImmediateUpdate(updateType, data = {}) {
  if (wsConnections.size === 0) return;

  try {
    const stats = await getBotStats();
    const updateData = {
      ...stats,
      immediateUpdate: true,
      updateType,
      updateData: data,
      timestamp: Date.now()
    };

    const message = JSON.stringify(updateData);
    wsConnections.forEach(ws => {
      if (ws.readyState === ws.OPEN) {
        ws.send(message);
      }
    });

    console.log(`📡 Broadcasted immediate ${updateType} update to ${wsConnections.size} clients`);
  } catch (error) {
    console.error('Error broadcasting immediate update:', error);
  }
}

// WebSocket connection with enhanced data
wss.on('connection', (ws) => {
  console.log('Client connected');
  wsConnections.add(ws);
  
  // Send initial data including historical prices
  const sendInitialData = async () => {
    try {
      // Get current bot stats
      const stats = await getBotStats();
      
      // Send existing server price history to new client
      if (serverPriceHistory.length > 0) {
        stats.historicalPrices = serverPriceHistory;
        console.log(`Sending ${serverPriceHistory.length} historical price points to new client`);
      } else {
        // If no server history, try to get some initial data
        const historicalPrices = await getHistoricalPrices();
        stats.historicalPrices = historicalPrices;
        // Also populate server history with this initial data
        serverPriceHistory = [...historicalPrices];
      }
      
      // Send data
      ws.send(JSON.stringify(stats));
    } catch (error) {
      console.error('Error sending initial data:', error);
    }
  };
  
  sendInitialData();
  
  // Set up regular price updates
  const interval = setInterval(async () => {
    try {
      // Get latest prices for all symbols
      const allSymbols = getActiveSymbols();
      const bestBidAskResponse = await RobinhoodAPI.getBestBidAsk(allSymbols);

      // Get other bot stats
      const stats = await getBotStats();

      // Initialize multi-symbol price data
      const symbolPrices = {};
      let currentPrice = 0; // For backward compatibility

      // Check if we have results in the expected format
      if (bestBidAskResponse && bestBidAskResponse.results && bestBidAskResponse.results.length) {
        // Process all symbol prices
        bestBidAskResponse.results.forEach(priceData => {
          const symbol = priceData.symbol;
          const price = parseFloat(priceData.price || priceData.ask_inclusive_of_buy_spread || 0);

          if (price > 0) {
            symbolPrices[symbol] = {
              price: price,
              change: 0, // TODO: Calculate 24h change
              timestamp: Date.now()
            };

            // No default symbol bias - all currencies are equal
          }
        });

        // Use any available price for legacy compatibility (prefer BTC if available)
        const btcPriceData = bestBidAskResponse.results.find(r => r.symbol === 'BTC-USD');
        if (btcPriceData) {
          currentPrice = parseFloat(btcPriceData.price || btcPriceData.ask_inclusive_of_buy_spread);
        } else if (bestBidAskResponse.results.length > 0) {
          // Fallback to first available price if BTC not available
          const firstPriceData = bestBidAskResponse.results[0];
          currentPrice = parseFloat(firstPriceData.price || firstPriceData.ask_inclusive_of_buy_spread);
        }

        if (currentPrice > 0) {
          stats.btcPrice = currentPrice.toString();

          // Add to server price history with memory management
          const newPricePoint = {
            timestamp: new Date().toISOString(),
            price: currentPrice
          };

          serverPriceHistory.push(newPricePoint);

          if (serverPriceHistory.length > MAX_SERVER_HISTORY) {
            serverPriceHistory = serverPriceHistory.slice(-MAX_SERVER_HISTORY);
          }

          // Include the new price point in the update
          stats.newPricePoint = newPricePoint;

          // Include strategy status updates if portfolio manager is active
          const strategy = getCurrentStrategy();
          if (strategy && strategy.constructor.name === 'PortfolioManager') {
            try {
              // Update strategy price histories with current price
              strategy.updatePrice(currentPrice);

              // Update centralized candle service with current price
              try {
                const candleService = getCandleService();
                if (candleService) {
                  candleService.updatePrice(currentPrice);
                }
              } catch (error) {
                console.error(`[ERROR] Failed to update candle service:`, error);
              }

              // Event-driven system: No need to poll strategies for status
              // Strategies signal when ready via events, status is maintained by PortfolioManager

              // Include strategy status in the update
              stats.strategyStatus = await strategy.getStats();
            } catch (error) {
              console.error('Error updating strategy status:', error);
            }
          }

          console.log(`[SERVER] Sending updated prices for ${Object.keys(symbolPrices).length} symbols`);
        }
      }

      // Add multi-symbol price data to stats
      stats.symbolPrices = symbolPrices;

      // Get multi-symbol holdings data
      try {
        const multiSymbolHoldings = await getMultiSymbolHoldings();
        stats.multiSymbolHoldings = multiSymbolHoldings;
      } catch (error) {
        console.error('Error getting multi-symbol holdings:', error);
      }

      // Send update
      ws.send(JSON.stringify(stats));
    } catch (error) {
      console.error('Error sending update:', error);
    }
  }, 5000); // Update every 5 seconds for real-time strategy card updates
  
  ws.on('close', () => {
    console.log('Client disconnected');
    wsConnections.delete(ws);
    clearInterval(interval);
  });
});

// Get current prices for all symbols
async function getMultiSymbolPrices() {
  try {
    const allSymbols = getActiveSymbols();
    const bestBidAskResponse = await RobinhoodAPI.getBestBidAsk(allSymbols);
    const symbolPrices = {};

    if (bestBidAskResponse && bestBidAskResponse.results) {
      bestBidAskResponse.results.forEach(priceData => {
        const symbol = priceData.symbol;
        const price = parseFloat(priceData.price || priceData.ask_inclusive_of_buy_spread || 0);

        if (price > 0) {
          symbolPrices[symbol] = price;
        }
      });
    }

    return symbolPrices;
  } catch (error) {
    console.error('Error getting multi-symbol prices:', error);
    return {};
  }
}

// Get holdings for all symbols
async function getMultiSymbolHoldings() {
  try {
    const holdings = await RobinhoodAPI.getHoldings();
    const multiSymbolHoldings = {};

    // Initialize all symbols with zero holdings
    const allSymbols = getActiveSymbols();
    allSymbols.forEach(symbol => {
      const assetCode = symbol.split('-')[0];
      multiSymbolHoldings[symbol] = {
        quantity: 0,
        value: 0,
        assetCode: assetCode
      };
    });

    // Update with actual holdings
    if (holdings && holdings.results) {
      holdings.results.forEach(holding => {
        const assetCode = holding.asset_code;
        const symbol = `${assetCode}-USD`;
        const quantity = parseFloat(holding.total_quantity || holding.quantity || 0);

        if (multiSymbolHoldings[symbol]) {
          multiSymbolHoldings[symbol].quantity = quantity;
          // Value will be calculated on frontend with current prices
        }
      });
    }

    return multiSymbolHoldings;
  } catch (error) {
    console.error('Error getting multi-symbol holdings:', error);
    return {};
  }
}

async function sendBotStats(ws) {
  try {
    const stats = await getBotStats();
    ws.send(JSON.stringify(stats));
  } catch (error) {
    console.error('Error sending stats:', error);
  }
}

// Function to get historical price data from Robinhood
async function getHistoricalPrices() {
  try {
    // Get current prices for all symbols
    const allSymbols = getActiveSymbols();
    const bestBidAskResponse = await RobinhoodAPI.getBestBidAsk(allSymbols);

    // Check if we have results in the expected format
    if (!bestBidAskResponse || !bestBidAskResponse.results || !bestBidAskResponse.results.length) {
      console.error('Failed to get current prices from Robinhood');
      return [];
    }

    // Use BTC price for legacy compatibility, fallback to first available
    const btcPriceData = bestBidAskResponse.results.find(r => r.symbol === 'BTC-USD');
    const currentPriceData = btcPriceData || bestBidAskResponse.results[0];
    const currentPrice = parseFloat(currentPriceData.price || currentPriceData.ask_inclusive_of_buy_spread);
    const timestamp = currentPriceData.timestamp || new Date().toISOString();

    console.log(`Current ${currentPriceData.symbol} price from Robinhood: $${currentPrice}`);

    // For historical data, we can try to get recent orders to see price history
    const recentOrders = await RobinhoodAPI.getRecentOrders(currentPriceData.symbol, 100);
    
    // Start with the current price
    const prices = [{
      timestamp: timestamp,
      price: currentPrice
    }];
    
    // Add prices from recent orders if available
    if (recentOrders && recentOrders.results && recentOrders.results.length) {
      console.log(`Found ${recentOrders.results.length} historical orders`);
      
      recentOrders.results.forEach(order => {
        if (order.executions && order.executions.length) {
          order.executions.forEach(execution => {
            prices.push({
              timestamp: execution.timestamp,
              price: parseFloat(execution.effective_price)
            });
          });
        }
      });
    }
    
    // Sort by timestamp (oldest first)
    prices.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    
    return prices;
  } catch (error) {
    console.error('Error fetching historical prices from Robinhood:', error);
    return [];
  }
}

// Update rebalance frequency
app.post('/api/portfolio/rebalance-frequency', express.json(), async (req, res) => {
  try {
    const { frequency } = req.body;

    // Import and use the config validator
    const { ConfigValidator } = await import('./config-validator.js');
    const validator = new ConfigValidator();
    const validation = validator.validateRebalanceFrequency(frequency);

    if (!validation.isValid) {
      return res.status(400).json({
        error: validation.error,
        corrected: validation.corrected,
        hours: validation.corrected / (60 * 60 * 1000)
      });
    }

    const strategy = getCurrentStrategy();
    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      return res.status(500).json({ error: 'Portfolio manager not active' });
    }

    // Update the rebalance frequency with validated value
    strategy.config.rebalanceFrequency = validation.corrected;
    console.log(`📊 Rebalance frequency updated to ${validation.corrected / (60 * 60 * 1000)} hours`);

    // Save the configuration
    saveConfig();

    res.json({
      success: true,
      frequency: validation.corrected,
      hours: validation.corrected / (60 * 60 * 1000)
    });
  } catch (error) {
    console.error('Error updating rebalance frequency:', error);
    res.status(500).json({ error: error.message });
  }
});

const PORT = process.env.PORT || 8182;
const HOST = process.env.HOST || '************'; // Tailscale VPN interface

server.listen(PORT, HOST, async () => {
  console.log(`✅ Server + WebSocket running at http://${HOST}:${PORT}`);
  console.log(`🌐 Accessible via Tailscale VPN on: http://${HOST}:${PORT}`);
  console.log(`🔒 Bot API calls will use default network interface (not Tailscale)`);

  // Auto-start the bot after server is ready
  console.log(`🤖 Auto-starting trading bot...`);
  try {
    await startBot();
    console.log(`✅ Trading bot started automatically`);
  } catch (error) {
    console.error(`❌ Failed to auto-start bot:`, error.message);
  }
});

// Export functions for use by other modules
export { getCurrentStrategy, getActiveSymbols, getSymbolConfig, broadcastImmediateUpdate };
