import { BaseStrategy } from './BaseStrategy.js';

export class MeanReversionStrategy extends BaseStrategy {
  constructor(config = {}, symbol = 'BTC-USD') {
    super({
      meanPeriod: 20,
      deviationThreshold: 0.04, // 4% below SMA
      ...config
    }, symbol);
  }

  shouldEnterTrade(currentPrice, marketData) {
    // Get the symbol we're evaluating (from marketData if available, otherwise use default)
    const evaluationSymbol = marketData?.symbol || this.symbol;
    const assetCode = evaluationSymbol.split('-')[0];

    // Don't enter if we already have a position
    if (this.hasPosition(marketData)) {
      const position = marketData.currentPosition;
      return {
        shouldEnter: false,
        reason: `Already holding ${position.quantity.toFixed(8)} ${assetCode} at avg price $${position.averagePrice.toFixed(2)}`
      };
    }

    const { meanPeriod, deviationThreshold } = this.config;

    if (this.priceHistory.length < meanPeriod) {
      return {
        shouldEnter: false,
        reason: `Accumulating price data (${this.priceHistory.length}/${meanPeriod} required)`
      };
    }

    const sma = this.calculateSMA(this.priceHistory, meanPeriod);
    const deviation = (sma - currentPrice) / sma;
    const deviationPercent = deviation * 100;
    const thresholdPercent = deviationThreshold * 100;

    const signal = deviation >= deviationThreshold;

    if (signal) {
      return {
        shouldEnter: true,
        reason: `Oversold: price $${currentPrice.toFixed(2)} is ${deviationPercent.toFixed(1)}% below ${meanPeriod}-period mean $${sma.toFixed(2)}`
      };
    } else if (deviation > 0) {
      return {
        shouldEnter: false,
        reason: `Waiting for deeper oversold: need ${thresholdPercent.toFixed(1)}% below mean, currently ${deviationPercent.toFixed(1)}% below $${sma.toFixed(2)}`
      };
    } else {
      return {
        shouldEnter: false,
        reason: `Price above mean: $${currentPrice.toFixed(2)} vs ${meanPeriod}-period mean $${sma.toFixed(2)} (${Math.abs(deviationPercent).toFixed(1)}% above)`
      };
    }
  }

  shouldExitTrade(currentPrice, marketData) {
    // Safety check: Don't allow exit if no position exists
    if (!this.hasPosition(marketData)) {
      return {
        shouldExit: false,
        reason: 'Safety check: No position to exit'
      };
    }

    const { meanPeriod, deviationThreshold } = this.config;

    if (this.priceHistory.length < meanPeriod) {
      return {
        shouldExit: false,
        reason: `Insufficient data for exit analysis (${this.priceHistory.length}/${meanPeriod} required)`
      };
    }

    const sma = this.calculateSMA(this.priceHistory, meanPeriod);
    const deviation = (currentPrice - sma) / sma;
    const deviationPercent = deviation * 100;

    // Exit when price returns close to mean or goes above it (mean reversion complete)
    // Also exit if price goes significantly above mean (overbought)
    const exitThreshold = deviationThreshold * 0.1; // Exit when 90% of entry deviation is recovered (very conservative)
    const overboughtThreshold = deviationThreshold; // Exit if price goes as far above mean as entry threshold

    if (deviation >= overboughtThreshold) {
      return {
        shouldExit: true,
        reason: `Overbought: price $${currentPrice.toFixed(2)} is ${deviationPercent.toFixed(1)}% above ${meanPeriod}-period mean $${sma.toFixed(2)} (threshold: ${(overboughtThreshold * 100).toFixed(1)}%)`
      };
    }

    if (deviation >= -exitThreshold) {
      return {
        shouldExit: true,
        reason: `Mean reversion complete: price $${currentPrice.toFixed(2)} recovered to ${deviationPercent.toFixed(1)}% from ${meanPeriod}-period mean $${sma.toFixed(2)}`
      };
    }

    return {
      shouldExit: false,
      reason: `Holding position: price $${currentPrice.toFixed(2)} still ${Math.abs(deviationPercent).toFixed(1)}% below mean $${sma.toFixed(2)} (exit at ${(exitThreshold * 100).toFixed(1)}% recovery)`
    };
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Buys when price deviates significantly below the moving average. Suitable for ranging markets.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      meanPeriod: {
        type: 'number',
        label: 'SMA Period',
        min: 5,
        max: 100,
        default: 20,
        description: 'Period used to calculate mean (SMA)'
      },
      deviationThreshold: {
        type: 'number',
        label: 'Deviation Threshold (%)',
        min: 0.01,
        max: 0.1,
        step: 0.005,
        default: 0.04,
        description: 'How far below mean price must be to trigger entry (as a percent)'
      }
    };
  }
}
